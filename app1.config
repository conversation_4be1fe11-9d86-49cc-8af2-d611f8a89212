DB_HOST="localhost"
DB_PORT="5432"
DB_USER="postgres"
DB_NAME="manager"
DB_PASS="postgres"
SERVER_HOST="localhost"
SERVER_PORT=""
SERVER_DB_HOST="localhost"
SERVER_DB_PORT="5432"
SERVER_DB_USER="postgres"
SERVER_DB_NAME="manager"
SERVER_DB_PASS="postgres"
RECREATE_DB="false"
FILE_DB_PATH="filedb"
MIGRATION_PATH="db/migrations"
LOG_DIR="logs"
CENTRAL_REPO_URL="https://**************:18088/api"
FILE_SERVER_URL="http://127.0.0.1:6060/api"
SECURE_HTTP_SERVER="true"
LOG_LEVEL="info"
