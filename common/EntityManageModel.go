package common

type EntityManageModel int

const (
	PACKAGE EntityManageModel = iota + 1
	CONFIGURATION
	DEPLOYMENT
	DEPLOYMENT_BUNDLE
	AGENT
	AGENT_TASK
	AGENT_TASK_RESULT
	COMPLIANCES
	DEPLOYMENT_POLICY
	PATCH
	PATCH_LANGUAGE
	PATCH_CATEGORY
	PATCH_PRODUCT
	WINDOW_PATCH
	ASSET_PATCH_VIEW
	ASSET_PATCH_RELATION
	PATCH_OS_APPLICATION
	PATCH_ASSET_APPLICATION
	ASSET
	SYSTEM_ACTION
	UBUNTU_PATCH
	UBUNTU_NOTICE_DATA
	LINUX_PACKAGES
	UBUNTU_RELEASE_PACKAGES
	LINUX_OS_APPLICATION
	PATCH_PREFERENCE
	COMPUTER_GROUP
	PATCH_DECLINE_POLICY
	AUTO_PATCH_DEPLOY
	AUTO_PATCH_TEST
	MAC_PATCH
	THIRD_PARTY_PACKAGE
	VIEW_ASSET_PATCH_CVE_RELATION
	MSRC_VULNERABILITY
	APPLICATION_CONTROL_POLICY
	FILE_SERVER_CONFIG
	REDHAT_AGENT_NOMINATION
	REDHAT_PATCH
	REDHAT_ADVAISORY
	REDHAT_RELEASE_PACKAGES
	REDHAT_FILE_SYNC_HISTORY
)

func (r EntityManageModel) String() string {
	switch r {
	case PACKAGE:
		return "package"
	case CONFIGURATION:
		return "configuration"
	case DEPLOYMENT:
		return "deployment"
	case DEPLOYMENT_BUNDLE:
		return "deployment_bundles"
	case AGENT_TASK:
		return "agent_tasks"
	case AGENT_TASK_RESULT:
		return "agent_task_results"
	case COMPLIANCES:
		return "compliances"
	case DEPLOYMENT_POLICY:
		return "deployment_policies"
	case PATCH:
		return "patch"
	case PATCH_PRODUCT:
		return "patch_products"
	case PATCH_CATEGORY:
		return "patch_categories"
	case PATCH_LANGUAGE:
		return "languages"
	case WINDOW_PATCH:
		return "windows_patches"
	case ASSET_PATCH_VIEW:
		return "asset_patch"
	case ASSET_PATCH_RELATION:
		return "asset_patch_relation"
	case PATCH_OS_APPLICATION:
		return "patch_os_applications"
	case PATCH_ASSET_APPLICATION:
		return "patch_asset_applications"
	case ASSET:
		return "asset"
	case SYSTEM_ACTION:
		return "system_actions"
	case UBUNTU_PATCH:
		return "ubuntu_patches"
	case UBUNTU_NOTICE_DATA:
		return "ubuntu_notice_data"
	case LINUX_PACKAGES:
		return "linux_packages"
	case UBUNTU_RELEASE_PACKAGES:
		return "ubuntu_release_packages"
	case LINUX_OS_APPLICATION:
		return "linux_os_application"
	case PATCH_PREFERENCE:
		return "patch_preference"
	case COMPUTER_GROUP:
		return "computer_groups"
	case PATCH_DECLINE_POLICY:
		return "patch_decline_policy"
	case AUTO_PATCH_DEPLOY:
		return "auto_patch_deployment"
	case AUTO_PATCH_TEST:
		return "auto_patch_test"
	case MAC_PATCH:
		return "mac_patches"
	case THIRD_PARTY_PACKAGE:
		return "third_party_packages"
	case VIEW_ASSET_PATCH_CVE_RELATION:
		return "view_asset_patch_cve_relation"
	case MSRC_VULNERABILITY:
		return "msrc_vulnerabilities"
	case APPLICATION_CONTROL_POLICY:
		return "application_control_policies"
	case FILE_SERVER_CONFIG:
		return "file_server_config"
	case REDHAT_AGENT_NOMINATION:
		return "redhat_agent_nomination"
	case REDHAT_PATCH:
		return "redhat_patches"
	case REDHAT_ADVAISORY:
		return "redhat_advaisory"
	case REDHAT_RELEASE_PACKAGES:
		return "redhat_release_packages"
	case REDHAT_FILE_SYNC_HISTORY:
		return "redhat_file_sync_history"
	default:
		return ""
	}
}
