package common

import (
	"crypto/sha256"
	"deployment/cache"
	"deployment/constant"
	"deployment/logger"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gorilla/mux"
	"gopkg.in/yaml.v3"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"
)

func GetUserFromCallContext() int64 {
	userId := constant.CallContext.Value(constant.UserId)
	if userId != nil {
		return userId.(int64)
	}
	return int64(0)
}

func GetEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if len(value) == 0 {
		return defaultValue
	}
	return value
}

func GetEnvNumeric(key string, defaultValue int) int {
	value := os.Getenv(key)
	if len(value) == 0 {
		return defaultValue
	}
	result, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}

	return result
}

func CurrentMillisecond() int64 {
	currentTimeNano := time.Now().UnixNano()
	currentTimeMillis := currentTimeNano / int64(time.Millisecond)
	return currentTimeMillis
}

func GetRequestBody(r *http.Request) []byte {
	body, err := io.ReadAll(r.Body)
	if err != nil {
		logger.ServiceLogger.Error("Error reading response body:", err)
		return nil
	}
	return body
}

func RestToJson(w http.ResponseWriter, rest any) (string, error) {
	w.Header().Set("Content-Type", "application/json")
	jsonData, err := json.Marshal(&rest)
	if err != nil {
		return "", err
	}
	logger.ServiceLogger.Trace(string(jsonData))
	return string(jsonData), nil
}

func RestToBytes(w http.ResponseWriter, rest any) ([]byte, error) {
	w.Header().Set("Content-Type", "application/json")
	jsonData, err := json.Marshal(&rest)
	if err != nil {
		return jsonData, err
	}
	return jsonData, nil
}

func UploadFileToFileDB(handler *multipart.FileHeader, err error, file multipart.File) (bool, error) {
	uploadDirectory := FileDirectoryPath()
	if _, err := os.Stat(uploadDirectory); os.IsNotExist(err) {
		err := os.Mkdir(uploadDirectory, os.ModePerm)
		if err != nil {
			logger.ServiceLogger.Error("Error creating directory:", err)
		}
	}

	filePath := filepath.Join(uploadDirectory, handler.Filename)
	newFile, err := os.Create(filePath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error creating the file"))
		return false, err
	}

	_, err = file.Seek(0, io.SeekStart)
	if err != nil {
		return false, err
	}

	_, err = io.Copy(newFile, file)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error cpoying the file"))
		return false, err
	}
	defer func(newFile *os.File) {
		err := newFile.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(newFile)
	return true, nil
}

func UploadPatchFile(handler *multipart.FileHeader, err error, file multipart.File, patchName string) (bool, error) {
	uploadDirectory := PatchFilePath(patchName)
	if _, err := os.Stat(uploadDirectory); os.IsNotExist(err) {
		err := os.Mkdir(uploadDirectory, os.ModePerm)
		if err != nil {
			logger.ServiceLogger.Error("Error creating directory:", err)
		}
	}

	filePath := filepath.Join(uploadDirectory, handler.Filename)
	newFile, err := os.Create(filePath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error creating the file"))
		return false, err
	}

	_, err = file.Seek(0, io.SeekStart)
	if err != nil {
		return false, err
	}

	_, err = io.Copy(newFile, file)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error cpoying the file"))
		return false, err
	}
	defer func(newFile *os.File) {
		err := newFile.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(newFile)
	return true, nil
}

func GenerateFileCheckSum(file multipart.File) (string, error) {
	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}
	hashInBytes := hash.Sum(nil)
	checksum := hex.EncodeToString(hashInBytes)
	return checksum, nil
}

func FileDirectoryPath() string {
	currentDir, _ := os.Getwd()
	return PrepareFilePath(currentDir, GetEnv("FILE_DB_PATH", "filedb"))
}

func PatchFilePath(patchName string) string {
	patchesDir := PrepareFilePath(FileDirectoryPath(), "patches")
	if _, err := os.Stat(patchesDir); os.IsNotExist(err) {
		err := os.Mkdir(patchesDir, os.ModePerm)
		if err != nil {
			logger.ServiceLogger.Error("Error creating directory:", err)
		}
	}
	patchDir := PrepareFilePath(patchesDir, patchName)
	if _, err := os.Stat(patchDir); os.IsNotExist(err) {
		err := os.Mkdir(patchDir, os.ModePerm)
		if err != nil {
			logger.ServiceLogger.Error("Error creating directory:", err)
		}
	}
	return patchDir
}

func PatchXmlPath() string {
	patchDbPath := filepath.Join(FileDirectoryPath(), "patchxml")
	_, err := os.Stat(patchDbPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(patchDbPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error("Error creating directory:", err)
		}
	}
	return patchDbPath
}

func PatchCabPath() string {
	patchDbPath := filepath.Join(FileDirectoryPath(), "patchcab")
	_, err := os.Stat(patchDbPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(patchDbPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error("Error creating directory:", err)
		}
	}
	return patchDbPath
}

func PatchDbPath() string {
	patchDbPath := filepath.Join(FileDirectoryPath(), "patchDb")
	_, err := os.Stat(patchDbPath)
	if os.IsNotExist(err) {
		err := os.MkdirAll(patchDbPath, 0755)
		if err != nil {
			logger.ServiceLogger.Error("Error creating directory:", err)
		}
	}
	return patchDbPath
}

func CentralRepoUrl() string {
	return GetEnv("CENTRAL_REPO_URL", "https://**************:18088/api")
}

func FileServerUrl() string {
	return GetEnv("FILE_SERVER_URL", "http://localhost:8081/api")
}

func CentralRepoAuthToken() string {
	return GetEnv("CENTRAL_REPO_TOKEN", "Apikey qwertyuiopASDFGHJKLzxcvbnm")
}

func FileServerAuthToken() string {
	return GetEnv("FILE_SERVER_TOKEN", "Apikey qwertyuiopASDFGHJKLzxcvbnm")
}

func PrepareFilePath(path ...string) string {
	return filepath.Join(path...)
}

func MainServerUrl() string {
	serverUrl := GetEnv("MAIN_SERVER_URL", "")
	if serverUrl != "" {
		return serverUrl
	}
	if "" == GetEnv("SERVER_PORT", "") {
		return "https://" + GetEnv("SERVER_HOST", "localhost")
	} else {
		return "https://" + GetEnv("SERVER_HOST", "localhost") + ":" + GetEnv("SERVER_PORT", "")
	}
}

func ServerUrl() string {
	if "" == GetEnv("PORT", "") {
		return "https://" + GetEnv("HTTP_SERVER_HOST", "localhost")
	} else {
		return "https://" + GetEnv("HTTP_SERVER_HOST", "localhost") + ":" + GetEnv("PORT", "8088")
	}
}

func PrepareInDiffMap(dbKey string, oldVal any, newVal any, diffMap *map[string]map[string]interface{}) *map[string]map[string]interface{} {
	(*diffMap)[dbKey] = map[string]interface{}{
		"oldvalue": oldVal,
		"newvalue": newVal,
	}

	return diffMap
}

func UpdateAuditStringWithFromTo(displayName string, valueMap map[string]interface{}) string {
	return fmt.Sprintf("%v : %v to %v, ", displayName, valueMap["oldvalue"], valueMap["newvalue"])
}

func ConvertToLong(string string) (int64, error) {
	id, err := strconv.ParseInt(string, 10, 64)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func ConvertToInt(str string) (int, error) {
	id, err := strconv.Atoi(str)
	if err != nil {
		return 0, err
	}
	return id, nil
}

func ConvertToInt64(val interface{}) int64 {
	switch v := val.(type) {
	case int64:
		return v
	case float64:
		return int64(v)
	case string:
		if intValue, err := strconv.ParseInt(v, 10, 64); err == nil {
			return intValue
		} else {
			fmt.Println("Failed to convert string to int64")
		}
	default:
		fmt.Println("Unsupported type for conversion")
	}
	return 0
}

func ToInt(value interface{}) int {
	switch v := value.(type) {
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if intValue, err := strconv.Atoi(v); err == nil {
			return intValue
		} else {
			fmt.Println("Failed to convert string to int64")
		}
	default:
		fmt.Println("Unsupported type for conversion")
	}
	return 0
}

func CurrentWorkingDir() string {
	WorkingDir, _ := os.Getwd()
	return WorkingDir
}

var matchFirstCap = regexp.MustCompile("(.)([A-Z][a-z]+)")
var matchAllCap = regexp.MustCompile("([a-z0-9])([A-Z])")

func ToSnakeCase(str string) string {
	snake := matchFirstCap.ReplaceAllString(str, "${1}_${2}")
	snake = matchAllCap.ReplaceAllString(snake, "${1}_${2}")
	return strings.ToLower(snake)
}

func StringContainsInList(slice []string, target string) bool {
	for _, value := range slice {
		if value == target {
			return true
		}
	}
	return false
}

func ToMilliSecond(timeUnit string, timeValue int64) int64 {
	return ToSecond(timeUnit, timeValue) * 1000
}

func ToSecond(timeUnit string, timeValue int64) int64 {
	switch timeUnit {
	case "second":
		return timeValue
	case "minute":
		return timeValue * 60
	case "hour":
		return timeValue * 3600
	case "day":
		return timeValue * 86400
	case "week":
		return timeValue * 86400 * 7
	case "month":
		return timeValue * 86400 * 30
	case "year":
		return timeValue * 86400 * 365
	default:
		return 0
	}
}

func ConvertIntToString(i any) string {
	return fmt.Sprintf("%d", i)
}

func ParseIdFromApiPath(w http.ResponseWriter, r *http.Request) int64 {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	if idString == "0" {
		jsonData, _ := RestToJson(w, Error("Error while getting id from api path  : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			return 0
		}
		return 0
	}

	intValue, err := ConvertToLong(idString)
	if err != nil {
		jsonData, _ := RestToJson(w, Error("Invalid id : "+idString, http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			return 0
		}
		return 0
	}
	return intValue
}

func SubtractStringList(existingKbIds, newlyFetchedKbs []string) []string {
	var result []string
	existingMap := make(map[string]bool)
	for _, kbID := range existingKbIds {
		existingMap[kbID] = true
	}
	for _, kbID := range newlyFetchedKbs {
		if !existingMap[kbID] {
			result = append(result, kbID)
		}
	}
	return result
}

func GetKeyList(m map[string]bool) []string {
	keys := make([]string, 0, len(m))

	for key := range m {
		keys = append(keys, key)
	}

	return keys
}

func StringStartsWithAny(name string, prefixes ...string) bool {
	name = strings.ReplaceAll(strings.TrimSpace(strings.Replace(name, "Microsoft", "", -1)), ",", "")
	for _, prefix := range prefixes {
		if strings.HasPrefix(name, prefix) {
			return true
		}
	}
	return false
}

func ChunkList(strings []string, chunkSize int) [][]string {
	var chunks [][]string
	for i := 0; i < len(strings); i += chunkSize {
		end := i + chunkSize
		if end > len(strings) {
			end = len(strings)
		}
		chunks = append(chunks, strings[i:end])
	}
	return chunks
}

func GetAccessibleAssetIds() []string {
	var assetIdList []string
	usrDepartment := constant.CallContext.Value(constant.UserDepartment)
	assetIds, exist := cache.DepartmentAssetCache.Get(ConvertIntToString(usrDepartment))
	if exist && assetIds != nil && len(assetIds) > 0 {
		assetIdList = append(assetIdList, assetIds...)
	}
	return assetIdList
}

func AddIfNotExist(slice []int64, element int64) []int64 {
	for _, e := range slice {
		if e == element {
			// Element already exists, return the original slice
			return slice
		}
	}
	// Element does not exist, append it to the slice
	return append(slice, element)
}

func IsBase64Encoded(s string) bool {
	_, err := base64.StdEncoding.DecodeString(s)
	return err == nil
}

// ReadYamlFile reads a YAML file and returns its content as a map[string]interface{}
func ReadYamlFile(filePath string) (map[string]interface{}, error) {
	// Read the file
	data, err := os.ReadFile(filePath)
	if err != nil {
		logger.ServiceLogger.Error("Error reading YAML file:", err)
		return nil, err
	}

	// Parse YAML content
	var result map[string]interface{}
	err = yaml.Unmarshal(data, &result)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing YAML file:", err)
		return nil, err
	}

	return result, nil
}

// ParseDateToMilliseconds parses various date formats and returns milliseconds
func ParseDateToMilliseconds(dateStr string) (int64, error) {
	// Common date formats used in RedHat XML files
	formats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05-07:00",
		"2006-01-02",
		"Mon Jan 2 15:04:05 MST 2006",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			return t.UnixNano() / int64(time.Millisecond), nil
		}
	}

	return 0, fmt.Errorf("unable to parse date: %s", dateStr)
}

// SQL Injection Prevention Utilities

// EscapeSQLString escapes single quotes in SQL string values to prevent SQL injection
func EscapeSQLString(value string) string {
	return strings.ReplaceAll(value, "'", "''")
}

// SanitizeSQLIdentifier validates and sanitizes SQL identifiers (column names, table names)
// Only allows alphanumeric characters, underscores, and dots
func SanitizeSQLIdentifier(identifier string) string {
	// Remove any characters that are not alphanumeric, underscore, or dot
	reg := regexp.MustCompile(`[^a-zA-Z0-9_.]`)
	sanitized := reg.ReplaceAllString(identifier, "")

	// Ensure it doesn't start with a number
	if len(sanitized) > 0 && unicode.IsDigit(rune(sanitized[0])) {
		sanitized = "_" + sanitized
	}

	return sanitized
}

// ValidateSQLOperator validates that the operator is in the allowed list
func ValidateSQLOperator(operator string) bool {
	allowedOperators := map[string]bool{
		"=":                   true,
		"!=":                  true,
		"<":                   true,
		">":                   true,
		"<=":                  true,
		">=":                  true,
		"LIKE":                true,
		"ILIKE":               true,
		"NOT LIKE":            true,
		"NOT ILIKE":           true,
		"IN":                  true,
		"NOT IN":              true,
		"IS NULL":             true,
		"IS NOT NULL":         true,
		"@>":                  true,
		"contains":            true,
		"not_contains":        true,
		"equals":              true,
		"not_equals":          true,
		"in":                  true,
		"not_in":              true,
		"is_null":             true,
		"is_not_null":         true,
		"contains_with_in":    true,
		"field_list_contains": true,
	}
	return allowedOperators[strings.ToLower(operator)]
}

// SanitizeStringValue sanitizes string values for SQL queries
func SanitizeStringValue(value string) string {
	// Remove null bytes
	value = strings.ReplaceAll(value, "\x00", "")

	// Escape single quotes
	value = EscapeSQLString(value)

	// Limit length to prevent extremely long inputs
	if len(value) > 1000 {
		value = value[:1000]
	}

	return value
}

// ValidateTableName validates that the table name is in the allowed list
func ValidateTableName(tableName string) bool {
	allowedTables := map[string]bool{
		"patch":                    true,
		"agent_tasks":              true,
		"audits":                   true,
		"assets":                   true,
		"deployments":              true,
		"ubuntu_patches":           true,
		"windows_patches":          true,
		"mac_patches":              true,
		"linux_os_applications":    true,
		"msrc_vulnerabilities":     true,
		"packages":                 true,
		"file_data":                true,
		"asset_patch_relation":     true,
		"asset_patch_cve_relation": true,
	}
	return allowedTables[strings.ToLower(tableName)]
}
