package service

import (
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/rest"
	"deployment/service/redhat"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"
)

type PatchSyncService struct {
}

func NewPatchSyncService() *PatchSyncService {
	return &PatchSyncService{}
}

func (service PatchSyncService) ExecutePatchSync() {
	pchPreferenceService := NewPatchPreferenceService()
	pchPreference, _ := pchPreferenceService.Get()
	if !pchPreference.IsPatchSyncRunning {
		logger.ServiceLogger.Info("starting patch sync...")
		pchPreference.IsPatchSyncRunning = true
		pchPreferenceService.Update(pchPreference)

		logger.ServiceLogger.Info("starting patch category sync...")
		service.executePatchCategorySync()
		logger.ServiceLogger.Info("patch category sync completed...")
		logger.ServiceLogger.Info("starting patch language sync...")
		service.executePatchLanguageSync()
		logger.ServiceLogger.Info("patch language sync completed...")

		if strings.Contains(strings.Join(pchPreference.EnabledPatchOs, ", "), common.Windows.String()) {
			logger.ServiceLogger.Info("starting patch product sync for windows...")
			service.executePatchProductSync()
			logger.ServiceLogger.Info("patch product sync completed...")
			logger.ServiceLogger.Info("starting windows patch sync...")
			service.executeWindowsSync()
			logger.ServiceLogger.Info("windows patch sync completed...")
			logger.ServiceLogger.Info("starting cab sync history...")
			service.executeCabHistorySync()
			logger.ServiceLogger.Info("cab sync history sync completed...")
		}
		//
		if pchPreference.EnableThirdPartyPatching || strings.Contains(strings.Join(pchPreference.EnabledPatchOs, ", "), common.Windows.String()) {
			logger.ServiceLogger.Info("starting xml sync...")
			service.executeDownloadXml()
			logger.ServiceLogger.Info("xml sync completed...")
		}

		if strings.Contains(strings.Join(pchPreference.EnabledPatchOs, ", "), common.Ubuntu.String()) {
			logger.ServiceLogger.Info("starting ubuntu patch sync...")
			service.executeUbuntuPatchSync()
			logger.ServiceLogger.Info("ubuntu patch sync completed...")
			logger.ServiceLogger.Info("starting ubuntu patch sync...")
			service.executeUbuntuNoticeSync()
			logger.ServiceLogger.Info("ubuntu patch sync completed...")
			logger.ServiceLogger.Info("starting ubuntu release package sync...")
			service.executeUbuntuReleasePackagesSync()
			logger.ServiceLogger.Info("ubuntu release package sync completed...")
			logger.ServiceLogger.Info("starting linux package sync...")
			service.executeLinuxPackagesSync()
			logger.ServiceLogger.Info("linux package sync completed...")
		}

		if strings.Contains(strings.Join(pchPreference.EnabledPatchOs, ", "), common.MacOS.String()) {
			logger.ServiceLogger.Info("starting mac patches sync...")
			service.executeMacSync()
			logger.ServiceLogger.Info("mac patches sync completed...")
			logger.ServiceLogger.Info("starting mac zip sync...")
			service.executeDownloadMacZip()
			logger.ServiceLogger.Info("mac zip sync completed...")
		}

		if strings.Contains(strings.Join(pchPreference.EnabledPatchOs, ", "), common.Redhat.String()) {
			logger.ServiceLogger.Info("starting Redhat patches sync...")
			err := redhat.NewRedhatPatchService().SyncRedHatPatch()
			if err != nil {
				logger.ServiceLogger.Error("Error in Redhat patch syncing : ", err.Error())
			}
			logger.ServiceLogger.Info("RedHat patches sync completed...")
		}

		if pchPreference.EnableThirdPartyPatching {
			logger.ServiceLogger.Info("starting third party patch sync...")
			service.executeThirdPartyPackageSync()
			logger.ServiceLogger.Info("third party patch sync completed...")
		}
		logger.ServiceLogger.Info("patch sync completed...")
		pchPreference, _ = pchPreferenceService.Get()
		pchPreference.IsPatchSyncRunning = false
		pchPreference.LastPatchSyncTime = time.Now().UnixMilli()
		pchPreferenceService.Update(pchPreference)
	} else {
		logger.ServiceLogger.Info("patch sync already running")
	}
}

func (service PatchSyncService) executePatchCategorySync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/category/search", common.CentralRepoUrl())
	lastUpdatedTime := int64(0)
	qual := rest.Qualification{}

	osAppService := NewPatchOsApplicationService()
	if lastUpdatedTime > 0 {
		qual.Column = "updated_time"
		qual.Condition = ">="
		qual.Value = strconv.FormatInt(lastUpdatedTime, 10)
	}
	offset := 0
	limit := 1000
	totalCount := 0
	shouldExecute := true
	for shouldExecute {
		var filter rest.SearchFilter
		if lastUpdatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id"}
		}
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil && pageResponse.TotalCount > 0 {
				totalCount = pageResponse.TotalCount
				var patchCategoryList []rest.PatchCategoryRest
				data, _ := json.Marshal(pageResponse.ObjectList)
				json.Unmarshal(data, &patchCategoryList)
				for _, categoryRest := range patchCategoryList {
					osApp := rest.PatchOsApplicationRest{}
					osApp.Platform = common.Windows.String()
					osApp.UUID = categoryRest.UUID
					osApp.ProductFamily = categoryRest.ParentName
					osApp.ProductFamilyUUID = categoryRest.ParentUUID
					osApp.GroupName = osAppService.GetGroupName(categoryRest.Name)
					osApp.Name = categoryRest.Name
					if strings.Contains(constant.PatchAllowedCategory, categoryRest.UUID) {
						osApp.Hidden = true
					}
					osAppService.CreatePatchOsApplication(osApp)
				}
			}
		}
		offset += limit
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executePatchLanguageSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/language/search", common.CentralRepoUrl())
	offset := 0
	limit := 1000
	totalCount := 0
	shouldExecute := true
	for shouldExecute {
		filter := rest.SearchFilter{Offset: offset, Size: limit}
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil && pageResponse.TotalCount > 0 {
				totalCount = pageResponse.TotalCount
				var languageList []rest.LanguageRest
				data, _ := json.Marshal(pageResponse.ObjectList)
				json.Unmarshal(data, &languageList)
				go NewLanguageService().BulkCreate(languageList)
			}
		}
		offset += limit
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executePatchProductSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/product/search", common.CentralRepoUrl())
	lastUpdatedTime := int64(0)
	qual := rest.Qualification{}
	product, err := NewPatchProductService().Repository.GetLatestPatchProduct()
	if err == nil {
		lastUpdatedTime = product.UpdatedTime
	}

	if lastUpdatedTime > 0 {
		qual.Column = "updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(lastUpdatedTime, 10)
	}
	offset := 0
	limit := 1000
	totalCount := 0
	shouldExecute := true
	for shouldExecute {
		var filter rest.SearchFilter
		if lastUpdatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,uuid", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,uuid"}
		}
		logger.PatchPoolingLogger.Debug("patch product filter :", filter)
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		logger.PatchPoolingLogger.Debug("patch product response status :", success)
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("patch product response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var productList []rest.PatchProductRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &productList)
					NewPatchProductService().BulkCreateOrUpdate(productList)
				}
			} else {
				logger.PatchPoolingLogger.Error("patch product json parsing error : ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("patch product sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeWindowsSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/windows/search", common.CentralRepoUrl())
	lastUpdatedTime := int64(0)
	qual := rest.Qualification{}
	patch, err := NewWindowsPatchService().Repository.GetLatestWindowsPatch()
	if err == nil {
		lastUpdatedTime = patch.LastUpdatedTime
	}

	if lastUpdatedTime > 0 {
		qual.Column = "last_updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(lastUpdatedTime, 10)
	}
	logger.PatchPoolingLogger.Info("Windows last_updated_time :", lastUpdatedTime)
	offset := 0
	limit := 2500
	totalCount := 0
	shouldExecute := true
	for shouldExecute {
		var filter rest.SearchFilter
		if lastUpdatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "last_updated_time,id,uuid", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "last_updated_time,id,uuid"}
		}
		logger.PatchPoolingLogger.Debug("Windows filter :", filter)
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		logger.PatchPoolingLogger.Debug("windows response success :", success)
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("windows response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var wPatchList []rest.WindowsPatchRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &wPatchList)
					NewWindowsPatchService().BulkCreateOrUpdate(wPatchList)
				}
			} else {
				logger.PatchPoolingLogger.Error("windows patch json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("windows patch sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeCabHistorySync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/cab/history/search", common.CentralRepoUrl())
	lastSyncTime := int64(0)
	qualification := rest.Qualification{}
	syncHistory, err := NewCabSyncHistoryService().Repository.GetLatestCabSyncHistory()
	if err == nil {
		lastSyncTime = syncHistory.LastSyncTime
	}
	if lastSyncTime > 0 {
		qualification.Column = "last_sync_time"
		qualification.Operator = ">="
		qualification.Value = strconv.FormatInt(lastSyncTime, 10)
	}
	logger.PatchPoolingLogger.Info("Cab Sync History last_sync_time :", lastSyncTime)
	offset := 0
	limit := 2500
	totalCount := 0
	shouldExecute := true
	for shouldExecute {
		var filter rest.SearchFilter
		if lastSyncTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "last_sync_time,id", Qualification: []rest.Qualification{qualification}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "last_sync_time,id"}
		}
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		logger.PatchPoolingLogger.Debug("cab sync history response success :", success)
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("cab sync history count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var syncHistoryRests []rest.CabSyncHistoryRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					err := json.Unmarshal(data, &syncHistoryRests)
					if err != nil {
						logger.ServiceLogger.Error("cab sync history json parsing error : ", err.Error())
					}
					for _, historyRest := range syncHistoryRests {
						service.DownloadPatchCabByFileName(historyRest.ReleaseDate)
						path := filepath.Join(common.PatchCabPath(), historyRest.ReleaseDate+".7z")
						destDir := filepath.Join(common.PatchCabPath(), historyRest.ReleaseDate)
						if _, err := os.Stat(path); os.IsNotExist(err) {
							logger.ServiceLogger.Warn("fail to download cab dir for : " + historyRest.ReleaseDate)
						} else {
							if _, err := os.Stat(destDir); os.IsExist(err) {
								err := os.Remove(destDir)
								if err != nil {
									logger.ServiceLogger.Warn("fail to remove old cab dir for : " + historyRest.ReleaseDate)
								}
							}
							if runtime.GOOS == "windows" {
								exec.Command(`C:\Program Files\7-Zip\7z.exe`, "x", path, fmt.Sprintf("-o%s", destDir)).Run()
							} else {
								exec.Command("7z", "x", path, fmt.Sprintf("-o%s", destDir)).Run()
							}
							NewCabSyncHistoryService().CreateOrUpdate(historyRest)
						}
					}
				}
			} else {
				logger.PatchPoolingLogger.Error("cab sync history json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("cab sync history sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeDownloadXml() {
	_, err := os.Stat(common.PatchXmlPath())
	if os.IsNotExist(err) {
		os.Mkdir(common.PatchXmlPath(), 0777)
	}
	logger.ServiceLogger.Info("api call to get xml list")
	response, success := common.ExecuteGetRequest(
		fmt.Sprintf("%s/patch/windows/get-all-category-xml-list", common.CentralRepoUrl()),
		map[string]string{"Authorization": common.CentralRepoAuthToken()})
	if success && response != nil && len(response) > 0 {
		logger.ServiceLogger.Info("response received for xml list")
		var result map[string][]string
		err := json.Unmarshal(response, &result)
		if err == nil && len(result) > 0 {
			fileNameList := result["result"]
			for _, fileName := range fileNameList {
				service.DownloadPatchXmlByFileName(fileName)
			}
		}
	} else {
		logger.ServiceLogger.Info("error while api call for xml list")
	}
}

func (service PatchSyncService) ExecuteDownloadCab(filename string) string {
	uuid := strings.ToUpper(filename)
	patch, err := NewWindowsPatchService().GetWindowsPatchByUuid(uuid)
	if err == nil && patch.Id > 0 && patch.CabExist {
		releaseDate := time.UnixMilli(patch.ReleaseDate)
		dateDirName := releaseDate.Format("2006-01")
		filePath := filepath.Join(common.PatchCabPath(), dateDirName)
		if _, err := os.Stat(filePath); os.IsNotExist(err) { // if main dir is not exist than download it from central repo
			service.DownloadPatchCabByFileName(dateDirName)
			path := filepath.Join(common.PatchCabPath(), dateDirName+".7z")
			destDir := filepath.Join(common.PatchCabPath(), dateDirName)
			if _, err := os.Stat(path); os.IsNotExist(err) {
				logger.ServiceLogger.Warn("fail to download cab dir for : " + dateDirName)
			} else {
				if _, err := os.Stat(destDir); os.IsExist(err) {
					err := os.Remove(destDir)
					if err != nil {
						logger.ServiceLogger.Warn("fail to remove old cab dir for : ", patch.ReleaseDate)
					}
				}
				if runtime.GOOS == "windows" {
					exec.Command(`C:\Program Files\7-Zip\7z.exe`, "x", path, fmt.Sprintf("-o%s", destDir)).Run()
				} else {
					exec.Command("7z", "x", path, fmt.Sprintf("-o%s", destDir)).Run()
				}
			}
		}
		filePath = filepath.Join(common.PatchCabPath(), dateDirName, uuid+".7z")
		if _, err := os.Stat(filePath); os.IsNotExist(err) { // if uuid.7z is not found than download dir from central repo
			service.DownloadPatchCabByFileName(dateDirName)
			path := filepath.Join(common.PatchCabPath(), dateDirName+".7z")
			destDir := filepath.Join(common.PatchCabPath(), dateDirName)
			if _, err := os.Stat(path); os.IsNotExist(err) {
				logger.ServiceLogger.Warn("fail to download cab dir for : " + dateDirName)
			} else {
				if _, err := os.Stat(destDir); os.IsExist(err) {
					err := os.Remove(destDir)
					if err != nil {
						logger.ServiceLogger.Warn("fail to remove old cab dir for : ", patch.ReleaseDate)
					}
				}
				if runtime.GOOS == "windows" {
					exec.Command(`C:\Program Files\7-Zip\7z.exe`, "x", path, fmt.Sprintf("-o%s", destDir)).Run()
				} else {
					exec.Command("7z", "x", path, fmt.Sprintf("-o%s", destDir)).Run()
				}
			}
		}
		return filePath
	}
	return ""
}

func (service PatchSyncService) DownloadPatchXmlByFileName(fileName string) {
	url := fmt.Sprintf("%s/xml/download/%s", common.CentralRepoUrl(), fileName)
	path := filepath.Join(common.PatchXmlPath(), fileName)
	common.ExecuteRequestToDownloadFile(
		url,
		path,
		map[string]string{"Authorization": common.CentralRepoAuthToken()})
}

func (service PatchSyncService) DownloadPatchCabByFileName(fileName string) {
	fileName = strings.ToUpper(fileName)
	url := fmt.Sprintf("%s/cab/download/%s", common.CentralRepoUrl(), fileName)
	path := filepath.Join(common.PatchCabPath(), fileName+".7z")
	logger.PatchPoolingLogger.Info("download file url : ", url, " file path :", path)
	isDownload := common.ExecuteRequestToDownloadFile(
		url,
		path,
		map[string]string{"Authorization": common.CentralRepoAuthToken()})
	logger.PatchPoolingLogger.Info("Status :", isDownload)
}

func (service PatchSyncService) executeUbuntuPatchSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/ubuntu-patch/search", common.CentralRepoUrl())
	lastUpdatedTime := int64(0)
	qual := rest.Qualification{}
	patch, err := NewUbuntuPatchService().Repository.GetLatestUbuntuPatch()
	if err == nil {
		lastUpdatedTime = patch.UpdatedTime
	}

	if lastUpdatedTime > 0 {
		qual.Column = "updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(lastUpdatedTime, 10)
	}

	offset := 0
	limit := 2500
	totalCount := 0
	shouldExecute := true

	for shouldExecute {
		var filter rest.SearchFilter
		if lastUpdatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,uuid", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,uuid"}
		}
		logger.PatchPoolingLogger.Debug("ubuntu filter :", filter)
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		logger.PatchPoolingLogger.Debug("ubuntu response success :", success)
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("ubuntu response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var patchList []rest.CPUbuntuPatchRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &patchList)
					NewUbuntuPatchService().BulkCreateOrUpdate(patchList)
				}
			} else {
				logger.PatchPoolingLogger.Error("ubuntu patch json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("ubuntu patch sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeUbuntuNoticeSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/ubuntu-notice-data/search", common.CentralRepoUrl())
	lastUpdatedTime := int64(0)
	qual := rest.Qualification{}
	patch, err := NewUbuntuNoticeDataService().Repository.GetLatestUbuntuNoticeData()
	if err == nil {
		lastUpdatedTime = patch.UpdatedTime
	}

	if lastUpdatedTime > 0 {
		qual.Column = "updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(lastUpdatedTime, 10)
	}

	offset := 0
	limit := 2500
	totalCount := 0
	shouldExecute := true

	for shouldExecute {
		var filter rest.SearchFilter
		if lastUpdatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,notice_id", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,notice_id"}
		}
		logger.PatchPoolingLogger.Debug("ubuntu notice filter :", filter)
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		logger.PatchPoolingLogger.Debug("ubuntu notice response success :", success)
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("ubuntu notice response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var patchList []rest.UbuntuNoticeDataRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &patchList)
					NewUbuntuNoticeDataService().BulkCreateOrUpdate(patchList)
				}
			} else {
				logger.PatchPoolingLogger.Error("ubuntu notice json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("ubuntu notice sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeUbuntuReleasePackagesSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/ubuntu-release-package/search", common.CentralRepoUrl())
	lastUpdatedTime := int64(0)
	qual := rest.Qualification{}
	patch, err := NewUbuntuReleasePackageService().Repository.GetLatestUbuntuReleasePackage()
	if err == nil {
		lastUpdatedTime = patch.UpdatedTime
	}

	if lastUpdatedTime > 0 {
		qual.Column = "updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(lastUpdatedTime, 10)
	}

	offset := 0
	limit := 2500
	totalCount := 0
	shouldExecute := true

	for shouldExecute {
		var filter rest.SearchFilter
		if lastUpdatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,name_and_version_unique_key", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,name_and_version_unique_key"}
		}
		logger.PatchPoolingLogger.Debug("ubuntu release filter :", filter)
		payload, _ = json.Marshal(filter)
		start := time.Now()
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		elapsed := time.Since(start)
		logger.PatchPoolingLogger.Debug("ubuntu release response success :", success, fmt.Sprintf(" request took: %s", elapsed))
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("ubuntu release response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var patchList []rest.UbuntuReleasePackageRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &patchList)
					start = time.Now()
					NewUbuntuReleasePackageService().BulkCreateOrUpdate(patchList)
					elapsed = time.Since(start)
					logger.PatchPoolingLogger.Debug("ubuntu release response process", fmt.Sprintf(" took: %s", elapsed))
				}
			} else {
				logger.PatchPoolingLogger.Error("ubuntu release json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("ubuntu release sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeLinuxPackagesSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/linux-package/search", common.CentralRepoUrl())
	lastUpdatedTime := int64(0)
	qual := rest.Qualification{}
	patch, err := NewLinuxPackageService().Repository.GetLatestLinuxPackage()
	if err == nil {
		lastUpdatedTime = patch.UpdatedTime
	}

	if lastUpdatedTime > 0 {
		qual.Column = "updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(lastUpdatedTime, 10)
	}

	offset := 0
	limit := 2500
	totalCount := 0
	shouldExecute := true

	for shouldExecute {
		var filter rest.SearchFilter
		if lastUpdatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,name,distribution,version", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,name,distribution,version"}
		}
		logger.PatchPoolingLogger.Debug("linux package filter :", filter)
		payload, _ = json.Marshal(filter)
		start := time.Now()
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		elapsed := time.Since(start)
		logger.PatchPoolingLogger.Debug("linux package response success :", success, fmt.Sprintf(" request took: %s", elapsed))
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("linux package response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var patchList []rest.LinuxPackageRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &patchList)
					start = time.Now()
					NewLinuxPackageService().BulkCreateOrUpdate(patchList)
					elapsed = time.Since(start)
					logger.PatchPoolingLogger.Debug("linux package response process", fmt.Sprintf(" took: %s", elapsed))

				}
			} else {
				logger.PatchPoolingLogger.Error("linux package json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("linux package sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeMacSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/mac-patch/search", common.CentralRepoUrl())
	updatedTime := int64(0)
	qual := rest.Qualification{}
	patch, err := NewMacPatchService().Repository.GetLatestMacPatch()
	if err == nil {
		updatedTime = patch.UpdatedTime
	}

	if updatedTime > 0 {
		qual.Column = "updated_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(updatedTime, 10)
	}

	offset := 0
	limit := 1000
	totalCount := 0
	shouldExecute := true

	for shouldExecute {
		var filter rest.SearchFilter
		if updatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,product_key", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "updated_time,id,product_key"}
		}
		logger.PatchPoolingLogger.Debug("mac patch filter :", filter)
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		logger.PatchPoolingLogger.Debug("mac patch response success :", success)
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("mac patch response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var macPatchList []rest.MacPatchRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &macPatchList)
					NewMacPatchService().BulkCreateOrUpdate(macPatchList)
				}
			} else {
				logger.PatchPoolingLogger.Error("mac patch json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("mac patch sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}

func (service PatchSyncService) executeDownloadMacZip() {
	_, err := os.Stat(common.PatchXmlPath())
	if os.IsNotExist(err) {
		os.Mkdir(common.PatchXmlPath(), 0777)
	}
	fileName := "mac.7z"
	filePath := filepath.Join(common.PatchXmlPath(), fileName)
	_, err = os.Stat(filePath)
	if !os.IsNotExist(err) {
		os.Remove(filePath)
	}
	service.DownloadPatchXmlByFileName(fileName)
	_, err = os.Stat(filePath)
	if !os.IsNotExist(err) {
		destDir := filepath.Join(common.PatchDbPath(), "mac")
		_, err := os.Stat(destDir)
		if os.IsNotExist(err) {
			os.Mkdir(destDir, 0777)
		}
		if runtime.GOOS == "windows" {
			exec.Command(`C:\Program Files\7-Zip\7z.exe`, "x", filePath, fmt.Sprintf("-o%s", destDir)).Run()
		} else {
			exec.Command("7z", "x", filePath, fmt.Sprintf("-o%s", destDir)).Run()
		}
	}
}

func (service PatchSyncService) executeThirdPartyPackageSync() {
	var pageResponse rest.ListResponseRest
	var payload []byte
	url := fmt.Sprintf("%s/patch/third-party/search", common.CentralRepoUrl())
	updatedTime := int64(0)
	qual := rest.Qualification{}

	if updatedTime > 0 {
		qual.Column = "created_time"
		qual.Operator = ">="
		qual.Value = strconv.FormatInt(updatedTime, 10)
	}

	offset := 0
	limit := 1000
	totalCount := 0
	shouldExecute := true

	for shouldExecute {
		var filter rest.SearchFilter
		if updatedTime > 0 {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "created_time,id,uuid", Qualification: []rest.Qualification{qual}}
		} else {
			filter = rest.SearchFilter{Offset: offset, Size: limit, SortBy: "created_time,id,uuid"}
		}
		logger.PatchPoolingLogger.Debug("third party package filter :", filter)
		payload, _ = json.Marshal(filter)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.CentralRepoAuthToken()})
		logger.PatchPoolingLogger.Debug("third party package response success :", success)
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pageResponse)
			if err == nil {
				logger.PatchPoolingLogger.Debug("third party package response count :", pageResponse.TotalCount)
				if pageResponse.TotalCount > 0 {
					totalCount = pageResponse.TotalCount
					var macPatchList []rest.ThirdPartyPackageRest
					data, _ := json.Marshal(pageResponse.ObjectList)
					json.Unmarshal(data, &macPatchList)
					NewThirdPartyPackageService().BulkCreateOrUpdate(macPatchList)
				}
			} else {
				logger.PatchPoolingLogger.Error("third party package json parsing error ", err.Error())
			}
		}
		offset += limit
		logger.PatchPoolingLogger.Debug("third party package sync : ", " offset: ", offset, " limit: ", limit, " totalCount: ", totalCount)
		shouldExecute = offset < totalCount
	}
}
