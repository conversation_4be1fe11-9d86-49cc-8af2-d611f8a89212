package redhat

import (
	"deployment/common"
	"deployment/converter"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type RedhatReleasePackagesService struct {
	Repository *repository.RedhatReleasePackagesRepository
}

func NewRedhatReleasePackagesService() *RedhatReleasePackagesService {
	return &RedhatReleasePackagesService{
		Repository: repository.NewRedhatReleasePackagesRepository(),
	}
}

func (service RedhatReleasePackagesService) GetRedhatReleasePackages(id int64) (rest.RedhatReleasePackagesRest, error) {
	pkg, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.RedhatReleasePackagesRest{}, err
	}
	return service.convertToRest(pkg), nil
}

func (service RedhatReleasePackagesService) Create(pkgRest rest.RedhatReleasePackagesRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Create RedhatReleasePackages process started")

	// Convert rest to model
	pkg := service.convertToModel(pkgRest)

	// Call repository create method
	id, err := service.Repository.Create(&pkg)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating RedhatReleasePackages: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create RedhatReleasePackages process completed successfully")
	return id, common.CustomError{}
}

func (service RedhatReleasePackagesService) Update(id int64, pkgRest rest.RedhatReleasePackagesRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Update RedhatReleasePackages process started for id - %v", id))

	// Get existing package
	pkg, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting RedhatReleasePackages for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusNotFound}
	}

	// Perform partial update
	diffMap, isUpdatable := service.performPartialUpdate(&pkg, pkgRest)
	if isUpdatable {
		// Handle base entity fields
		if pkgRest.UpdatedTime == 0 {
			pkg.UpdatedTime = common.CurrentMillisecond()
		} else {
			pkg.UpdatedTime = pkgRest.UpdatedTime
		}
		pkg.UpdatedById = common.GetUserFromCallContext()

		// Call repository update method
		_, err := service.Repository.Update(&pkg)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating RedhatReleasePackages for id - %v, Error: %s", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}

		go service.AfterUpdate(diffMap, pkg)
		logger.ServiceLogger.Info(fmt.Sprintf("Update RedhatReleasePackages process completed successfully for id - %v", id))
		return true, common.CustomError{}
	} else {
		return false, common.CustomError{}
	}
}

// CreateOrUpdate creates a new package or updates existing one based on sha256
func (service RedhatReleasePackagesService) CreateOrUpdate(pkgRest rest.RedhatReleasePackagesRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("CreateOrUpdate RedhatReleasePackages process started")

	// Check if package already exists
	existingPkg, err := service.Repository.FindBySha256(pkgRest.Sha256)
	if err != nil {
		// Package doesn't exist, create new one
		logger.ServiceLogger.Debug(fmt.Sprintf("Creating new package with sha256: %s", pkgRest.Sha256))
		return service.Create(pkgRest)
	} else {
		// Package exists, update it
		logger.ServiceLogger.Debug(fmt.Sprintf("Updating existing package id: %d, sha256: %s", existingPkg.Id, pkgRest.Sha256))
		_, updateErr := service.Update(existingPkg.Id, pkgRest)
		if updateErr.Message != "" {
			return 0, updateErr
		}
		return existingPkg.Id, common.CustomError{}
	}
}

// Helper methods for conversion and partial updates

func (service RedhatReleasePackagesService) convertToRest(pkg model.RedhatReleasePackages) rest.RedhatReleasePackagesRest {
	baseRest := converter.ConvertToBaseEntityRest(pkg.BaseEntityModel)
	return rest.RedhatReleasePackagesRest{
		BaseEntityRest: baseRest,
		PkgName:        pkg.PkgName,
		Filename:       pkg.Filename,
		Src:            pkg.Src,
		Release:        pkg.Release,
		Version:        pkg.Version,
		Sha256:         pkg.Sha256,
		Arch:           pkg.Arch,
		SrcVersionName: pkg.SrcVersionName,
		Sum:            pkg.Sum,
		SumType:        pkg.SumType,
		Epoch:          pkg.Epoch,
	}
}

func (service RedhatReleasePackagesService) convertToModel(pkgRest rest.RedhatReleasePackagesRest) model.RedhatReleasePackages {
	baseModel := converter.ConvertToBaseEntityModel(pkgRest.BaseEntityRest)
	return model.RedhatReleasePackages{
		BaseEntityModel: baseModel,
		PkgName:         pkgRest.PkgName,
		Filename:        pkgRest.Filename,
		Src:             pkgRest.Src,
		Release:         pkgRest.Release,
		Version:         pkgRest.Version,
		Sha256:          pkgRest.Sha256,
		Arch:            pkgRest.Arch,
		SrcVersionName:  pkgRest.SrcVersionName,
		Sum:             pkgRest.Sum,
		SumType:         pkgRest.SumType,
		Epoch:           pkgRest.Epoch,
	}
}

func (service RedhatReleasePackagesService) performPartialUpdate(pkg *model.RedhatReleasePackages, pkgRest rest.RedhatReleasePackagesRest) (map[string]map[string]interface{}, bool) {
	diffMap := make(map[string]map[string]interface{})
	isUpdatable := false

	// Update fields if they are provided and different
	if pkgRest.PkgName != "" && pkg.PkgName != pkgRest.PkgName {
		diffMap["PkgName"] = map[string]interface{}{"from": pkg.PkgName, "to": pkgRest.PkgName}
		pkg.PkgName = pkgRest.PkgName
		isUpdatable = true
	}

	if pkgRest.Version != "" && pkg.Version != pkgRest.Version {
		diffMap["Version"] = map[string]interface{}{"from": pkg.Version, "to": pkgRest.Version}
		pkg.Version = pkgRest.Version
		isUpdatable = true
	}

	if pkgRest.Release != "" && pkg.Release != pkgRest.Release {
		diffMap["Release"] = map[string]interface{}{"from": pkg.Release, "to": pkgRest.Release}
		pkg.Release = pkgRest.Release
		isUpdatable = true
	}

	// Add more field updates as needed...

	return diffMap, isUpdatable
}

func (service RedhatReleasePackagesService) AfterUpdate(diffMap map[string]map[string]interface{}, pkg model.RedhatReleasePackages) {
	// Audit creation removed as per requirement
}
