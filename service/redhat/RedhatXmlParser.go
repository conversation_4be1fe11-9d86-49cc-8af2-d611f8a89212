package redhat

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"encoding/xml"
	"fmt"
	"os"
	"strconv"
	"strings"
)

// XML structures for parsing RedHat updateinfo XML
type UpdateInfo struct {
	XMLName xml.Name `xml:"updates"`
	Updates []Update `xml:"update"`
}

type Update struct {
	XMLName     xml.Name   `xml:"update"`
	ID          string     `xml:"id"`
	Type        string     `xml:"type,attr"`
	Title       string     `xml:"title"`
	Severity    string     `xml:"severity"`
	Summary     string     `xml:"summary"`
	Description string     `xml:"description"`
	Solution    string     `xml:"solution"`
	References  References `xml:"references"`
	Issued      Issued     `xml:"issued"`
	Updated     Updated    `xml:"updated"`
	Rights      string     `xml:"rights"`
	PkgList     PkgList    `xml:"pkglist"`
	Reboot      Reboot     `xml:"reboot"`
}

type References struct {
	XMLName xml.Name    `xml:"references"`
	Refs    []Reference `xml:"reference"`
}

type Reference struct {
	XMLName xml.Name `xml:"reference"`
	Href    string   `xml:"href,attr"`
	ID      string   `xml:"id,attr"`
	Title   string   `xml:"title,attr"`
	Type    string   `xml:"type,attr"`
}

type Issued struct {
	XMLName xml.Name `xml:"issued"`
	Date    string   `xml:"date,attr"`
}

type Updated struct {
	XMLName xml.Name `xml:"updated"`
	Date    string   `xml:"date,attr"`
}

type PkgList struct {
	XMLName     xml.Name     `xml:"pkglist"`
	Collections []Collection `xml:"collection"`
}

type Collection struct {
	XMLName  xml.Name  `xml:"collection"`
	Short    string    `xml:"short,attr"`
	Name     string    `xml:"name"`
	Packages []Package `xml:"package"`
}

type Package struct {
	XMLName  xml.Name `xml:"package"`
	Name     string   `xml:"name,attr"`
	Version  string   `xml:"version,attr"`
	Release  string   `xml:"release,attr"`
	Epoch    string   `xml:"epoch,attr"`
	Arch     string   `xml:"arch,attr"`
	Src      string   `xml:"src,attr"`
	Filename string   `xml:"filename"`
	Sum      Sum      `xml:"sum"`
}

type Sum struct {
	XMLName xml.Name `xml:"sum"`
	Type    string   `xml:"type,attr"`
	Value   string   `xml:",chardata"`
}

type Reboot struct {
	XMLName   xml.Name `xml:"reboot"`
	Suggested bool     `xml:"suggested,attr"`
}

// XML structures for parsing RedHat primary XML
type Metadata struct {
	XMLName  xml.Name         `xml:"metadata"`
	Packages []PrimaryPackage `xml:"package"`
}

type PrimaryPackage struct {
	XMLName     xml.Name `xml:"package"`
	Type        string   `xml:"type,attr"`
	Name        string   `xml:"name"`
	Arch        string   `xml:"arch"`
	Version     Version  `xml:"version"`
	Checksum    Checksum `xml:"checksum"`
	Summary     string   `xml:"summary"`
	Description string   `xml:"description"`
	Packager    string   `xml:"packager"`
	URL         string   `xml:"url"`
	Time        Time     `xml:"time"`
	Size        Size     `xml:"size"`
	Location    Location `xml:"location"`
	Format      Format   `xml:"format"`
}

type Version struct {
	XMLName xml.Name `xml:"version"`
	Epoch   string   `xml:"epoch,attr"`
	Ver     string   `xml:"ver,attr"`
	Rel     string   `xml:"rel,attr"`
}

type Checksum struct {
	XMLName xml.Name `xml:"checksum"`
	Type    string   `xml:"type,attr"`
	PkgId   string   `xml:"pkgid,attr"`
	Value   string   `xml:",chardata"`
}

type Time struct {
	XMLName xml.Name `xml:"time"`
	File    string   `xml:"file,attr"`
	Build   string   `xml:"build,attr"`
}

type Size struct {
	XMLName   xml.Name `xml:"size"`
	Package   string   `xml:"package,attr"`
	Installed string   `xml:"installed,attr"`
	Archive   string   `xml:"archive,attr"`
}

type Location struct {
	XMLName xml.Name `xml:"location"`
	Href    string   `xml:"href,attr"`
}

type Format struct {
	XMLName     xml.Name  `xml:"format"`
	License     string    `xml:"license"`
	Vendor      string    `xml:"vendor"`
	Group       string    `xml:"group"`
	BuildHost   string    `xml:"buildhost"`
	SourceRpm   string    `xml:"sourcerpm"`
	HeaderRange string    `xml:"header-range"`
	Provides    Provides  `xml:"provides"`
	Requires    Requires  `xml:"requires"`
	Conflicts   Conflicts `xml:"conflicts"`
	Obsoletes   Obsoletes `xml:"obsoletes"`
	Files       Files     `xml:"file"`
}

type Provides struct {
	XMLName xml.Name `xml:"provides"`
	Entries []Entry  `xml:"entry"`
}

type Requires struct {
	XMLName xml.Name `xml:"requires"`
	Entries []Entry  `xml:"entry"`
}

type Conflicts struct {
	XMLName xml.Name `xml:"conflicts"`
	Entries []Entry  `xml:"entry"`
}

type Obsoletes struct {
	XMLName xml.Name `xml:"obsoletes"`
	Entries []Entry  `xml:"entry"`
}

type Entry struct {
	XMLName xml.Name `xml:"entry"`
	Name    string   `xml:"name,attr"`
	Flags   string   `xml:"flags,attr"`
	Epoch   string   `xml:"epoch,attr"`
	Ver     string   `xml:"ver,attr"`
	Rel     string   `xml:"rel,attr"`
}

type Files struct {
	XMLName xml.Name `xml:"file"`
	Value   string   `xml:",chardata"`
}

// RedhatXmlParser handles parsing of RedHat XML files
type RedhatXmlParser struct {
	advisoryService        *RedhatAdvaisoryService
	releasePackagesService *RedhatReleasePackagesService
	patchService           *RedhatPatchEntityService
}

func NewRedhatXmlParser() *RedhatXmlParser {
	return &RedhatXmlParser{
		advisoryService:        NewRedhatAdvaisoryService(),
		releasePackagesService: NewRedhatReleasePackagesService(),
		patchService:           NewRedhatPatchEntityService(),
	}
}

// ParseUpdateInfoXml parses updateinfo XML file and creates advisories and release packages
func (parser *RedhatXmlParser) ParseUpdateInfoXml(xmlFilePath string) error {
	logger.ServiceLogger.Debug(fmt.Sprintf("Parsing updateinfo XML file: %s", xmlFilePath))

	file, err := os.Open(xmlFilePath)
	if err != nil {
		return fmt.Errorf("failed to open XML file: %w", err)
	}
	defer file.Close()

	decoder := xml.NewDecoder(file)
	var updateInfo UpdateInfo

	err = decoder.Decode(&updateInfo)
	if err != nil {
		return fmt.Errorf("failed to decode XML: %w", err)
	}

	// Process each update
	for _, update := range updateInfo.Updates {
		if err := parser.processUpdate(update); err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error processing update %s: %s", update.ID, err.Error()))
			// Continue processing other updates
		}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Successfully processed %d updates from updateinfo XML", len(updateInfo.Updates)))
	return nil
}

// processUpdate processes a single update and creates advisory and release packages
func (parser *RedhatXmlParser) processUpdate(update Update) error {
	// Create advisory
	advisoryRest := rest.RedhatAdvaisoryRest{
		BaseEntityRest: rest.BaseEntityRest{
			CreatedTime: common.CurrentMillisecond(),
			UpdatedTime: common.CurrentMillisecond(),
			CreatedById: 0, // System user
			UpdatedById: 0, // System user
			OOB:         true,
			Removed:     false,
		},
		RhId:        update.ID,
		Title:       update.Title,
		Type:        update.Type,
		Description: update.Description,
		Severity:    update.Severity,
		Summary:     update.Summary,
		Solution:    update.Solution,
		Rights:      update.Rights,
		Issued:      update.Issued.Date,
		Updated:     update.Updated.Date,
		Reboot:      update.Reboot.Suggested,
	}

	// Convert references to string
	var refStrings []string
	for _, ref := range update.References.Refs {
		refStrings = append(refStrings, fmt.Sprintf("%s: %s (%s)", ref.Type, ref.Title, ref.Href))
	}
	advisoryRest.References = strings.Join(refStrings, "; ")

	// Extract CVE list
	var cveList []string
	for _, ref := range update.References.Refs {
		if ref.Type == "cve" {
			cveList = append(cveList, ref.ID)
		}
	}
	advisoryRest.CVEList = strings.Join(cveList, ",")

	// Extract Bugzilla IDs
	var bugzillaIds []string
	for _, ref := range update.References.Refs {
		if ref.Type == "bugzilla" {
			bugzillaIds = append(bugzillaIds, ref.ID)
		}
	}
	advisoryRest.BugzillaIds = strings.Join(bugzillaIds, ",")

	// Parse release date
	if update.Issued.Date != "" {
		if releaseDate, err := common.ParseDateToMilliseconds(update.Issued.Date); err == nil {
			advisoryRest.ReleaseDate = releaseDate
		}
	}

	// Create or update advisory
	_, customErr := parser.advisoryService.CreateOrUpdate(advisoryRest)
	if customErr.Message != "" {
		return fmt.Errorf("failed to create advisory: %s", customErr.Message)
	}

	// Process packages in the update
	for _, collection := range update.PkgList.Collections {
		for _, pkg := range collection.Packages {
			if err := parser.processReleasePackage(pkg, update.ID); err != nil {
				logger.ServiceLogger.Error(fmt.Sprintf("Error processing package %s: %s", pkg.Name, err.Error()))
				// Continue processing other packages
			}
		}
	}

	return nil
}

// processReleasePackage processes a single package and creates release package entry
func (parser *RedhatXmlParser) processReleasePackage(pkg Package, rhId string) error {
	pkgRest := rest.RedhatReleasePackagesRest{
		BaseEntityRest: rest.BaseEntityRest{
			Name:        rhId, // Use advisory ID as name
			CreatedTime: common.CurrentMillisecond(),
			UpdatedTime: common.CurrentMillisecond(),
			CreatedById: 0, // System user
			UpdatedById: 0, // System user
			OOB:         true,
			Removed:     false,
		},
		PkgName:        pkg.Name,
		Filename:       pkg.Filename,
		Src:            pkg.Src,
		Release:        pkg.Release,
		Version:        pkg.Version,
		Sha256:         pkg.Sum.Value,
		Arch:           pkg.Arch,
		SrcVersionName: rhId,
		Sum:            pkg.Sum.Value,
		SumType:        pkg.Sum.Type,
		Epoch:          pkg.Epoch,
	}

	// Create or update release package
	_, customErr := parser.releasePackagesService.CreateOrUpdate(pkgRest)
	if customErr.Message != "" {
		return fmt.Errorf("failed to create release package: %s", customErr.Message)
	}

	return nil
}

func (parser *RedhatXmlParser) ParsePrimaryXml(xmlFilePath string, context *RedhatContext) error {
	logger.ServiceLogger.Debug(fmt.Sprintf("Parsing primary XML file: %s", xmlFilePath))

	file, err := os.Open(xmlFilePath)
	if err != nil {
		return fmt.Errorf("failed to open XML file: %w", err)
	}
	defer file.Close()

	decoder := xml.NewDecoder(file)
	var metadata Metadata

	err = decoder.Decode(&metadata)
	if err != nil {
		return fmt.Errorf("failed to decode XML: %w", err)
	}

	// Process each package
	for _, pkg := range metadata.Packages {
		if err := parser.processPrimaryPackage(pkg, context); err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error processing package %s: %s", pkg.Name, err.Error()))
			// Continue processing other packages
		}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Successfully processed %d packages from primary XML", len(metadata.Packages)))
	return nil
}

func (parser *RedhatXmlParser) processPrimaryPackage(pkg PrimaryPackage, context *RedhatContext) error {
	// Check if patch already exists
	exists, err := parser.patchService.Repository.ExistsByPkgIdAndDistribution(pkg.Checksum.Value, context.GetDistributionWithOsVariant())
	if err != nil {
		return fmt.Errorf("error checking patch existence: %w", err)
	}

	if exists {
		logger.ServiceLogger.Debug(fmt.Sprintf("Patch already exists for pkgId: %s", pkg.Checksum.Value))
		return nil
	}

	// Check if there's an advisory for this package
	var advisory rest.RedhatAdvaisoryRest
	var hasAdvisory bool

	// Try to find advisory by source package name
	if pkg.Format.SourceRpm != "" {
		if releasePackage, err := parser.releasePackagesService.Repository.FindFirstBySrc(pkg.Format.SourceRpm); err == nil {
			if adv, err := parser.advisoryService.Repository.FindByRhId(releasePackage.Name); err == nil {
				advisory = parser.advisoryService.convertToRest(adv)
				hasAdvisory = true
			}
		}
	}

	// Only create patch if there's an associated advisory
	if !hasAdvisory {
		logger.ServiceLogger.Debug(fmt.Sprintf("No advisory found for package %s, skipping patch creation", pkg.Name))
		return nil
	}

	// Parse size
	var size int64
	if pkg.Size.Package != "" {
		if parsedSize, err := strconv.ParseInt(pkg.Size.Package, 10, 64); err == nil {
			size = parsedSize
		}
	}

	// Parse release date
	var releaseDate int64
	if pkg.Time.Build != "" {
		if parsedTime, err := strconv.ParseInt(pkg.Time.Build, 10, 64); err == nil {
			releaseDate = parsedTime * 1000 // Convert to milliseconds
		}
	}

	// Create name with version
	nameWithVersion := pkg.Name
	if pkg.Version.Ver != "" {
		nameWithVersion += "-" + pkg.Version.Ver
		if pkg.Version.Rel != "" {
			nameWithVersion += "-" + pkg.Version.Rel
		}
	}

	// Convert dependencies to strings
	var provides, requires, conflicts, obsoletes []string
	for _, entry := range pkg.Format.Provides.Entries {
		provides = append(provides, entry.Name)
	}
	for _, entry := range pkg.Format.Requires.Entries {
		requires = append(requires, entry.Name)
	}
	for _, entry := range pkg.Format.Conflicts.Entries {
		conflicts = append(conflicts, entry.Name)
	}
	for _, entry := range pkg.Format.Obsoletes.Entries {
		obsoletes = append(obsoletes, entry.Name)
	}

	parts := strings.Split(pkg.Location.Href, `/`)
	packageFullName := parts[len(parts)-1]

	// Create patch
	patchRest := rest.RedhatPatchRest{
		BaseEntityRest: rest.BaseEntityRest{
			CreatedTime: common.CurrentMillisecond(),
			UpdatedTime: common.CurrentMillisecond(),
			CreatedById: 0, // System user
			UpdatedById: 0, // System user
			OOB:         true,
			Removed:     false,
		},
		PkgId:            pkg.Checksum.Value,
		PkgName:          pkg.Name,
		Title:            advisory.Title,
		Version:          pkg.Version.Ver,
		ReleaseVersion:   pkg.Version.Rel,
		Arch:             pkg.Arch,
		Distribution:     context.GetDistributionWithOsVariant(),
		Severity:         advisory.Severity,
		BulletinId:       advisory.RhId,
		CVEIds:           advisory.CVEList,
		ReleaseDate:      releaseDate,
		LastModifiedDate: common.CurrentMillisecond(),
		SrcPkgName:       pkg.Format.SourceRpm,
		NameWithVersion:  nameWithVersion,
		DownloadUrl:      context.BaseUrl + "/" + pkg.Location.Href,
		Size:             size,
		Checksum:         pkg.Checksum.Value,
		ChecksumType:     pkg.Checksum.Type,
		Location:         pkg.Location.Href,
		Summary:          pkg.Summary,
		Description:      pkg.Description,
		Packager:         pkg.Packager,
		Vendor:           pkg.Format.Vendor,
		License:          pkg.Format.License,
		Group:            pkg.Format.Group,
		BuildHost:        pkg.Format.BuildHost,
		HeaderRange:      pkg.Format.HeaderRange,
		Provides:         strings.Join(provides, ","),
		Requires:         strings.Join(requires, ","),
		Conflicts:        strings.Join(conflicts, ","),
		Obsoletes:        strings.Join(obsoletes, ","),
		Files:            pkg.Format.Files.Value,
		PackageFullName:  packageFullName,
	}

	// Create patch
	_, customErr := parser.patchService.CreateOrUpdate(patchRest)
	if customErr.Message != "" {
		return fmt.Errorf("failed to create patch: %s", customErr.Message)
	}

	logger.ServiceLogger.Debug(fmt.Sprintf("Created patch for package: %s", pkg.Name))
	return nil
}
