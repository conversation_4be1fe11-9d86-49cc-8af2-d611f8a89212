package redhat

import (
	"deployment/common"
	"deployment/converter"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type RedhatAdvaisoryService struct {
	Repository *repository.RedhatAdvaisoryRepository
}

func NewRedhatAdvaisoryService() *RedhatAdvaisoryService {
	return &RedhatAdvaisoryService{
		Repository: repository.NewRedhatAdvaisoryRepository(),
	}
}

func (service RedhatAdvaisoryService) GetRedhatAdvaisory(id int64) (rest.RedhatAdvaisoryRest, error) {
	advisory, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.RedhatAdvaisoryRest{}, err
	}
	return service.convertToRest(advisory), nil
}

func (service RedhatAdvaisoryService) Create(advisoryRest rest.RedhatAdvaisoryRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Create RedhatAdvaisory process started")

	// Convert rest to model
	advisory := service.convertToModel(advisoryRest)

	// Call repository create method
	id, err := service.Repository.Create(&advisory)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating RedhatAdvaisory: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create RedhatAdvaisory process completed successfully")
	return id, common.CustomError{}
}

func (service RedhatAdvaisoryService) Update(id int64, advisoryRest rest.RedhatAdvaisoryRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Update RedhatAdvaisory process started for id - %v", id))

	// Get existing advisory
	advisory, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting RedhatAdvaisory for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusNotFound}
	}

	// Perform partial update
	diffMap, isUpdatable := service.performPartialUpdate(&advisory, advisoryRest)
	if isUpdatable {
		// Handle base entity fields
		if advisoryRest.UpdatedTime == 0 {
			advisory.UpdatedTime = common.CurrentMillisecond()
		} else {
			advisory.UpdatedTime = advisoryRest.UpdatedTime
		}
		advisory.UpdatedById = common.GetUserFromCallContext()

		// Call repository update method
		_, err := service.Repository.Update(&advisory)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating RedhatAdvaisory for id - %v, Error: %s", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}

		go service.AfterUpdate(diffMap, advisory)
		logger.ServiceLogger.Info(fmt.Sprintf("Update RedhatAdvaisory process completed successfully for id - %v", id))
		return true, common.CustomError{}
	} else {
		return false, common.CustomError{}
	}
}

// CreateOrUpdate creates a new advisory or updates existing one based on rhId
func (service RedhatAdvaisoryService) CreateOrUpdate(advisoryRest rest.RedhatAdvaisoryRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("CreateOrUpdate RedhatAdvaisory process started")

	// Check if advisory already exists
	existingAdvisory, err := service.Repository.FindByRhId(advisoryRest.RhId)
	if err != nil {
		// Advisory doesn't exist, create new one
		logger.ServiceLogger.Debug(fmt.Sprintf("Creating new advisory with rhId: %s", advisoryRest.RhId))
		return service.Create(advisoryRest)
	} else {
		// Advisory exists, update it
		logger.ServiceLogger.Debug(fmt.Sprintf("Updating existing advisory id: %d, rhId: %s", existingAdvisory.Id, advisoryRest.RhId))
		_, updateErr := service.Update(existingAdvisory.Id, advisoryRest)
		if updateErr.Message != "" {
			return 0, updateErr
		}
		return existingAdvisory.Id, common.CustomError{}
	}
}

// Helper methods for conversion and partial updates

func (service RedhatAdvaisoryService) convertToRest(advisory model.RedhatAdvaisory) rest.RedhatAdvaisoryRest {
	baseRest := converter.ConvertToBaseEntityRest(advisory.BaseEntityModel)
	return rest.RedhatAdvaisoryRest{
		BaseEntityRest: baseRest,
		RhId:           advisory.RhId,
		Title:          advisory.Title,
		Type:           advisory.Type,
		Description:    advisory.Description,
		Severity:       advisory.Severity,
		ReleaseDate:    advisory.ReleaseDate,
		Summary:        advisory.Summary,
		Solution:       advisory.Solution,
		References:     advisory.References,
		Rights:         advisory.Rights,
		Issued:         advisory.Issued,
		Updated:        advisory.Updated,
		CVEList:        advisory.CVEList,
		BugzillaIds:    advisory.BugzillaIds,
		Reboot:         advisory.Reboot,
		Restart:        advisory.Restart,
	}
}

func (service RedhatAdvaisoryService) convertToModel(advisoryRest rest.RedhatAdvaisoryRest) model.RedhatAdvaisory {
	baseModel := converter.ConvertToBaseEntityModel(advisoryRest.BaseEntityRest)
	return model.RedhatAdvaisory{
		BaseEntityModel: baseModel,
		RhId:            advisoryRest.RhId,
		Title:           advisoryRest.Title,
		Type:            advisoryRest.Type,
		Description:     advisoryRest.Description,
		Severity:        advisoryRest.Severity,
		ReleaseDate:     advisoryRest.ReleaseDate,
		Summary:         advisoryRest.Summary,
		Solution:        advisoryRest.Solution,
		References:      advisoryRest.References,
		Rights:          advisoryRest.Rights,
		Issued:          advisoryRest.Issued,
		Updated:         advisoryRest.Updated,
		CVEList:         advisoryRest.CVEList,
		BugzillaIds:     advisoryRest.BugzillaIds,
		Reboot:          advisoryRest.Reboot,
		Restart:         advisoryRest.Restart,
	}
}

func (service RedhatAdvaisoryService) performPartialUpdate(advisory *model.RedhatAdvaisory, advisoryRest rest.RedhatAdvaisoryRest) (map[string]map[string]interface{}, bool) {
	diffMap := make(map[string]map[string]interface{})
	isUpdatable := false

	// Update fields if they are provided and different
	if advisoryRest.Title != "" && advisory.Title != advisoryRest.Title {
		diffMap["Title"] = map[string]interface{}{"from": advisory.Title, "to": advisoryRest.Title}
		advisory.Title = advisoryRest.Title
		isUpdatable = true
	}

	if advisoryRest.Type != "" && advisory.Type != advisoryRest.Type {
		diffMap["Type"] = map[string]interface{}{"from": advisory.Type, "to": advisoryRest.Type}
		advisory.Type = advisoryRest.Type
		isUpdatable = true
	}

	if advisoryRest.Description != "" && advisory.Description != advisoryRest.Description {
		diffMap["Description"] = map[string]interface{}{"from": advisory.Description, "to": advisoryRest.Description}
		advisory.Description = advisoryRest.Description
		isUpdatable = true
	}

	if advisoryRest.Severity != "" && advisory.Severity != advisoryRest.Severity {
		diffMap["Severity"] = map[string]interface{}{"from": advisory.Severity, "to": advisoryRest.Severity}
		advisory.Severity = advisoryRest.Severity
		isUpdatable = true
	}

	// Add more field updates as needed...

	return diffMap, isUpdatable
}

func (service RedhatAdvaisoryService) AfterUpdate(diffMap map[string]map[string]interface{}, advisory model.RedhatAdvaisory) {
	// Audit creation removed as per requirement
}
