package redhat

import (
	"deployment/common"
	"deployment/converter"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
)

type RedhatPatchEntityService struct {
	Repository *repository.RedhatPatchRepository
}

func NewRedhatPatchEntityService() *RedhatPatchEntityService {
	return &RedhatPatchEntityService{
		Repository: repository.NewRedhatPatchRepository(),
	}
}

func (service RedhatPatchEntityService) GetRedhatPatch(id int64) (rest.RedhatPatchRest, error) {
	patch, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.RedhatPatchRest{}, err
	}
	return service.convertToRest(patch), nil
}

func (service RedhatPatchEntityService) Create(patchRest rest.RedhatPatchRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Create RedhatPatch process started")

	// Convert rest to model
	patch := service.convertToModel(patchRest)

	// Call repository create method
	id, err := service.Repository.Create(&patch)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating RedhatPatch: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create RedhatPatch process completed successfully")
	return id, common.CustomError{}
}

func (service RedhatPatchEntityService) Update(id int64, patchRest rest.RedhatPatchRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Update RedhatPatch process started for id - %v", id))

	// Get existing patch
	patch, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while getting RedhatPatch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusNotFound}
	}

	// Perform partial update
	diffMap, isUpdatable := service.performPartialUpdate(&patch, patchRest)
	if isUpdatable {
		// Handle base entity fields
		if patchRest.UpdatedTime == 0 {
			patch.UpdatedTime = common.CurrentMillisecond()
		} else {
			patch.UpdatedTime = patchRest.UpdatedTime
		}
		patch.UpdatedById = common.GetUserFromCallContext()

		// Call repository update method
		_, err := service.Repository.Update(&patch)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating RedhatPatch for id - %v, Error: %s", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}

		go service.AfterUpdate(diffMap, patch)
		logger.ServiceLogger.Info(fmt.Sprintf("Update RedhatPatch process completed successfully for id - %v", id))
		return true, common.CustomError{}
	} else {
		return false, common.CustomError{}
	}
}

func (service RedhatPatchEntityService) GetAllRedhatPatch(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.REDHAT_PATCH.String(), true, "")
	var responsePage rest.ListResponseRest
	var patches []model.RedhatPatch
	var err error

	// Count total records
	count, _ := service.Repository.CountByQuery(countQuery.Query, countQuery.Parameters)
	if count > 0 {
		// Fetch data based on search filter
		searchQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.REDHAT_PATCH.String(), false, "")
		patches, err = service.Repository.GetAllByQuery(searchQuery.Query, searchQuery.Parameters)
		if err != nil {
			return responsePage, err
		}
		// Convert patch list to rest list
		responsePage.ObjectList = service.convertListToRest(patches)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service RedhatPatchEntityService) Delete(id int64) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Delete RedhatPatch process started for id - %v", id))

	err := service.Repository.SoftDelete(id)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while deleting RedhatPatch for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info(fmt.Sprintf("Delete RedhatPatch process completed successfully for id - %v", id))
	return true, common.CustomError{}
}

// CreateOrUpdate creates a new patch or updates existing one based on pkgId and distribution
func (service RedhatPatchEntityService) CreateOrUpdate(patchRest rest.RedhatPatchRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("CreateOrUpdate RedhatPatch process started")

	// Check if patch already exists
	existingPatch, err := service.Repository.FindByPkgIdAndDistribution(patchRest.PkgId, patchRest.Distribution)
	if err != nil {
		// Patch doesn't exist, create new one
		logger.ServiceLogger.Debug(fmt.Sprintf("Creating new patch with pkgId: %s", patchRest.PkgId))
		return service.Create(patchRest)
	} else {
		// Patch exists, update it
		logger.ServiceLogger.Debug(fmt.Sprintf("Updating existing patch id: %d, pkgId: %s", existingPatch.Id, patchRest.PkgId))
		_, updateErr := service.Update(existingPatch.Id, patchRest)
		if updateErr.Message != "" {
			return 0, updateErr
		}
		return existingPatch.Id, common.CustomError{}
	}
}

// Helper methods for conversion and partial updates

func (service RedhatPatchEntityService) convertToRest(patch model.RedhatPatch) rest.RedhatPatchRest {
	baseRest := converter.ConvertToBaseEntityRest(patch.BaseEntityModel)
	return rest.RedhatPatchRest{
		BaseEntityRest:    baseRest,
		PkgId:             patch.PkgId,
		PkgName:           patch.PkgName,
		Title:             patch.Title,
		Version:           patch.Version,
		ReleaseVersion:    patch.ReleaseVersion,
		Arch:              patch.Arch,
		Distribution:      patch.Distribution,
		Severity:          patch.Severity,
		BulletinId:        patch.BulletinId,
		ChangeLog:         patch.ChangeLog,
		DependenciesPkgId: patch.DependenciesPkgId,
		CVEIds:            patch.CVEIds,
		ReleaseDate:       patch.ReleaseDate,
		LastModifiedDate:  patch.LastModifiedDate,
		SrcPkgName:        patch.SrcPkgName,
		NameWithVersion:   patch.NameWithVersion,
		Dependencies:      patch.Dependencies,
		DownloadUrl:       patch.DownloadUrl,
		Size:              patch.Size,
		Checksum:          patch.Checksum,
		ChecksumType:      patch.ChecksumType,
		Location:          patch.Location,
		Summary:           patch.Summary,
		Description:       patch.Description,
		Packager:          patch.Packager,
		Vendor:            patch.Vendor,
		License:           patch.License,
		Group:             patch.Group,
		BuildHost:         patch.BuildHost,
		HeaderRange:       patch.HeaderRange,
		Provides:          patch.Provides,
		Requires:          patch.Requires,
		Conflicts:         patch.Conflicts,
		Obsoletes:         patch.Obsoletes,
		Files:             patch.Files,
		PackageFullName:   patch.PackageFullName,
	}
}

func (service RedhatPatchEntityService) convertToModel(patchRest rest.RedhatPatchRest) model.RedhatPatch {
	baseModel := converter.ConvertToBaseEntityModel(patchRest.BaseEntityRest)
	return model.RedhatPatch{
		BaseEntityModel:   baseModel,
		PkgId:             patchRest.PkgId,
		PkgName:           patchRest.PkgName,
		Title:             patchRest.Title,
		Version:           patchRest.Version,
		ReleaseVersion:    patchRest.ReleaseVersion,
		Arch:              patchRest.Arch,
		Distribution:      patchRest.Distribution,
		Severity:          patchRest.Severity,
		BulletinId:        patchRest.BulletinId,
		ChangeLog:         patchRest.ChangeLog,
		DependenciesPkgId: patchRest.DependenciesPkgId,
		CVEIds:            patchRest.CVEIds,
		ReleaseDate:       patchRest.ReleaseDate,
		LastModifiedDate:  patchRest.LastModifiedDate,
		SrcPkgName:        patchRest.SrcPkgName,
		NameWithVersion:   patchRest.NameWithVersion,
		Dependencies:      patchRest.Dependencies,
		DownloadUrl:       patchRest.DownloadUrl,
		Size:              patchRest.Size,
		Checksum:          patchRest.Checksum,
		ChecksumType:      patchRest.ChecksumType,
		Location:          patchRest.Location,
		Summary:           patchRest.Summary,
		Description:       patchRest.Description,
		Packager:          patchRest.Packager,
		Vendor:            patchRest.Vendor,
		License:           patchRest.License,
		Group:             patchRest.Group,
		BuildHost:         patchRest.BuildHost,
		HeaderRange:       patchRest.HeaderRange,
		Provides:          patchRest.Provides,
		Requires:          patchRest.Requires,
		Conflicts:         patchRest.Conflicts,
		Obsoletes:         patchRest.Obsoletes,
		Files:             patchRest.Files,
		PackageFullName:   patchRest.PackageFullName,
	}
}

func (service RedhatPatchEntityService) convertListToRest(patches []model.RedhatPatch) []rest.RedhatPatchRest {
	var patchRestList []rest.RedhatPatchRest
	for _, patch := range patches {
		patchRestList = append(patchRestList, service.convertToRest(patch))
	}
	return patchRestList
}

func (service RedhatPatchEntityService) performPartialUpdate(patch *model.RedhatPatch, patchRest rest.RedhatPatchRest) (map[string]map[string]interface{}, bool) {
	diffMap := make(map[string]map[string]interface{})
	isUpdatable := false

	// Update fields if they are provided and different
	if patchRest.PkgName != "" && patch.PkgName != patchRest.PkgName {
		diffMap["PkgName"] = map[string]interface{}{"from": patch.PkgName, "to": patchRest.PkgName}
		patch.PkgName = patchRest.PkgName
		isUpdatable = true
	}

	if patchRest.Title != "" && patch.Title != patchRest.Title {
		diffMap["Title"] = map[string]interface{}{"from": patch.Title, "to": patchRest.Title}
		patch.Title = patchRest.Title
		isUpdatable = true
	}

	if patchRest.Version != "" && patch.Version != patchRest.Version {
		diffMap["Version"] = map[string]interface{}{"from": patch.Version, "to": patchRest.Version}
		patch.Version = patchRest.Version
		isUpdatable = true
	}

	if patchRest.Severity != "" && patch.Severity != patchRest.Severity {
		diffMap["Severity"] = map[string]interface{}{"from": patch.Severity, "to": patchRest.Severity}
		patch.Severity = patchRest.Severity
		isUpdatable = true
	}

	if patchRest.BulletinId != "" && patch.BulletinId != patchRest.BulletinId {
		diffMap["BulletinId"] = map[string]interface{}{"from": patch.BulletinId, "to": patchRest.BulletinId}
		patch.BulletinId = patchRest.BulletinId
		isUpdatable = true
	}

	// Add more field updates as needed...

	return diffMap, isUpdatable
}

func (service RedhatPatchEntityService) AfterUpdate(diffMap map[string]map[string]interface{}, patch model.RedhatPatch) {
	// Audit creation removed as per requirement
}
