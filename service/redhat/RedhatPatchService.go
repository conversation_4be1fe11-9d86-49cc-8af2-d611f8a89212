package redhat

import (
	"archive/zip"
	"compress/gzip"
	"deployment/common"
	"deployment/interfaces"
	"deployment/logger"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

type RedhatPatchService struct {
	xmlParser              *RedhatXmlParser
	fileSyncHistoryService *RedhatFileSyncHistoryService
}

// RedhatContext represents the context for processing RedHat patch data
type RedhatContext struct {
	FolderPath   string
	BaseUrl      string
	Distribution string
	OsVariant    string
	FinalPath    string
}

// NewRedhatContext creates a new RedhatContext instance
func NewRedhatContext(folderPath, baseUrl, distribution, osVariant, finalPath string) *RedhatContext {
	return &RedhatContext{
		FolderPath:   folderPath,
		BaseUrl:      baseUrl,
		Distribution: distribution,
		OsVariant:    osVariant,
		FinalPath:    finalPath,
	}
}

// GetDistributionWithOsVariant returns distribution combined with OS variant
func (ctx *RedhatContext) GetDistributionWithOsVariant() string {
	if ctx.OsVariant == "" {
		return ctx.Distribution
	}
	return ctx.Distribution + "-" + ctx.OsVariant
}

func NewRedhatPatchService() *RedhatPatchService {
	return &RedhatPatchService{
		xmlParser:              NewRedhatXmlParser(),
		fileSyncHistoryService: NewRedhatFileSyncHistoryService(),
	}
}

// UploadPatchMirrorFile uploads a file to the patch mirror directory structure
func (service RedhatPatchService) UploadPatchMirrorFile(fileHeader *multipart.FileHeader, file multipart.File, folderPath string) (map[string]interface{}, common.CustomError) {
	logger.ServiceLogger.Info("Process started to upload patch mirror file")

	// Validate inputs
	if fileHeader == nil || file == nil {
		return nil, common.CustomError{Message: "File is required", Code: http.StatusBadRequest}
	}

	if strings.TrimSpace(folderPath) == "" {
		return nil, common.CustomError{Message: "Folder path is required", Code: http.StatusBadRequest}
	}

	// Validate and sanitize folder path to prevent path traversal
	if err := service.validateFolderPath(folderPath); err != nil {
		return nil, common.CustomError{Message: err.Error(), Code: http.StatusBadRequest}
	}

	// Create the target directory structure
	targetDir := filepath.Join(common.FileDirectoryPath(), "patchmirror", folderPath)

	// Check if directory already exists and clean it
	if _, err := os.Stat(targetDir); err == nil {
		logger.ServiceLogger.Info(fmt.Sprintf("Directory %s already exists, cleaning it", targetDir))
		if err := service.cleanDirectory(targetDir); err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error cleaning directory %s: %s", targetDir, err.Error()))
			return nil, common.CustomError{Message: "Failed to clean existing directory", Code: http.StatusInternalServerError}
		}
	}

	if err := os.MkdirAll(targetDir, 0755); err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error creating directory %s: %s", targetDir, err.Error()))
		return nil, common.CustomError{Message: "Failed to create target directory", Code: http.StatusInternalServerError}
	}

	// Prepare file path with original filename
	originalFilename := fileHeader.Filename
	if strings.TrimSpace(originalFilename) == "" {
		return nil, common.CustomError{Message: "Invalid filename", Code: http.StatusBadRequest}
	}

	targetFilePath := filepath.Join(targetDir, originalFilename)

	// Upload the file
	success, err := service.uploadFileToPath(file, targetFilePath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error uploading file: %s", err.Error()))
		return nil, common.CustomError{Message: "Failed to upload file", Code: http.StatusInternalServerError}
	}

	if !success {
		return nil, common.CustomError{Message: "File upload failed", Code: http.StatusInternalServerError}
	}

	// Check if the uploaded file is a zip file and extract it
	if strings.ToLower(filepath.Ext(originalFilename)) == ".zip" {
		logger.ServiceLogger.Info(fmt.Sprintf("Extracting zip file: %s", originalFilename))
		if err := service.ExtractZipFile(targetFilePath, targetDir); err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error extracting zip file %s: %s", originalFilename, err.Error()))
			// Don't return error here, just log it as the file upload was successful
			logger.ServiceLogger.Warn("File uploaded successfully but extraction failed")
		} else {
			logger.ServiceLogger.Info(fmt.Sprintf("Successfully extracted zip file: %s", originalFilename))
		}
	}

	logger.ServiceLogger.Info("Upload patch mirror file process completed successfully")

	// Return success response
	response := map[string]interface{}{
		"message":    "File uploaded successfully",
		"filename":   originalFilename,
		"folderPath": folderPath,
		"targetPath": targetFilePath,
		"size":       fileHeader.Size,
	}

	return response, common.CustomError{}
}

// validateFolderPath validates and sanitizes the folder path to prevent path traversal attacks
func (service RedhatPatchService) validateFolderPath(folderPath string) error {
	// Remove leading/trailing whitespace
	folderPath = strings.TrimSpace(folderPath)

	// Check for path traversal attempts
	if strings.Contains(folderPath, "..") {
		return fmt.Errorf("path traversal not allowed")
	}

	// Check for absolute paths (should be relative)
	/*if filepath.IsAbs(folderPath) {
		return fmt.Errorf("absolute paths not allowed")
	}*/

	// Check for invalid characters
	invalidChars := []string{"<", ">", ":", "\"", "|", "?", "*"}
	for _, char := range invalidChars {
		if strings.Contains(folderPath, char) {
			return fmt.Errorf("invalid character '%s' in folder path", char)
		}
	}

	// Ensure path doesn't start with / or \
	if strings.HasPrefix(folderPath, "\\") {
		return fmt.Errorf("folder path should not start with \\")
	}

	return nil
}

// uploadFileToPath uploads a file to the specified path
func (service RedhatPatchService) uploadFileToPath(file multipart.File, targetPath string) (bool, error) {
	// Create the target file
	targetFile, err := os.Create(targetPath)
	if err != nil {
		return false, fmt.Errorf("error creating target file: %w", err)
	}
	defer func() {
		if closeErr := targetFile.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error closing target file: %s", closeErr.Error()))
		}
	}()

	// Reset file pointer to beginning
	if _, err := file.Seek(0, io.SeekStart); err != nil {
		return false, fmt.Errorf("error seeking file: %w", err)
	}

	// Copy file content
	if _, err := io.Copy(targetFile, file); err != nil {
		return false, fmt.Errorf("error copying file content: %w", err)
	}

	return true, nil
}

// UploadCertificateFile uploads a certificate file to the certificates directory based on asset OS variant
func (service RedhatPatchService) UploadCertificateFile(fileHeader *multipart.FileHeader, file multipart.File, assetId int64) (map[string]interface{}, common.CustomError) {
	logger.ServiceLogger.Info("Process started to upload certificate file")

	// Validate inputs
	if fileHeader == nil || file == nil {
		return nil, common.CustomError{Message: "File is required", Code: http.StatusBadRequest}
	}

	if assetId <= 0 {
		return nil, common.CustomError{Message: "Valid asset ID is required", Code: http.StatusBadRequest}
	}

	// Get asset information using AgentService
	agentService := interfaces.GetAgentService()
	if agentService == nil {
		logger.ServiceLogger.Error("AgentService not available for certificate upload")
		return nil, common.CustomError{Message: "Service not available", Code: http.StatusInternalServerError}
	}
	assetMetaData, err := agentService.GetAssetMetaDataById(assetId)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error getting asset metadata for assetId %d: %s", assetId, err.Error()))
		return nil, common.CustomError{Message: "Asset not found", Code: http.StatusBadRequest}
	}

	// Validate that asset has platform version information
	if strings.TrimSpace(assetMetaData.PlatformVersion) == "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Asset %d does not have platform version information", assetId))
		return nil, common.CustomError{Message: "Asset does not have OS variant information", Code: http.StatusBadRequest}
	}

	// Determine OS variant from asset platform version
	osVariant := service.DetermineOsVariantFromAsset(assetMetaData.PlatformVersion)
	if osVariant == "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Could not determine OS variant for asset %d with platform version: %s", assetId, assetMetaData.PlatformVersion))
		return nil, common.CustomError{Message: "Could not determine OS variant from asset information", Code: http.StatusBadRequest}
	}

	// Create the target directory structure
	targetDir := filepath.Join(common.FileDirectoryPath(), "certificates", "redhat", osVariant)
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error creating directory %s: %s", targetDir, err.Error()))
		return nil, common.CustomError{Message: "Failed to create target directory", Code: http.StatusInternalServerError}
	}

	// Prepare file path with original filename
	originalFilename := fileHeader.Filename
	if strings.TrimSpace(originalFilename) == "" {
		return nil, common.CustomError{Message: "Invalid filename", Code: http.StatusBadRequest}
	}

	targetFilePath := filepath.Join(targetDir, originalFilename)

	// Upload the file
	success, err := service.uploadFileToPath(file, targetFilePath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error uploading certificate file: %s", err.Error()))
		return nil, common.CustomError{Message: "Failed to upload file", Code: http.StatusInternalServerError}
	}

	if success {
		entries, err := os.ReadDir(targetDir)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		for _, entry := range entries {
			if entry.Type().IsRegular() {
				ext := strings.ToLower(filepath.Ext(entry.Name()))
				if ext == ".pem" || ext == ".p12" {
					fullPath := filepath.Join(targetDir, entry.Name())
					if err := os.Remove(fullPath); err != nil {
						logger.ServiceLogger.Error(fmt.Sprintf("failed to remove file %s: %w"), fullPath, err)
					}
				}
			}
		}
	}

	if !success {
		return nil, common.CustomError{Message: "File upload failed", Code: http.StatusInternalServerError}
	}

	logger.ServiceLogger.Info("Upload certificate file process completed successfully")

	// Return success response
	response := map[string]interface{}{
		"message":         "Certificate file uploaded successfully",
		"filename":        originalFilename,
		"assetId":         assetId,
		"osVariant":       osVariant,
		"targetPath":      targetFilePath,
		"size":            fileHeader.Size,
		"platformVersion": assetMetaData.PlatformVersion,
	}

	return response, common.CustomError{}
}

// determineOsVariantFromAsset determines the OS variant (server/workstation) from asset platform version
func (service RedhatPatchService) DetermineOsVariantFromAsset(platformVersion string) string {
	// Check if the platform version contains Red Hat information
	if !strings.Contains(platformVersion, "Red Hat") {
		logger.ServiceLogger.Warn(fmt.Sprintf("Platform version does not contain Red Hat information: %s", platformVersion))
		return ""
	}

	// Check for workstation variant
	if strings.Contains(strings.ToLower(platformVersion), "workstation") {
		return "workstation"
	}

	// Default to server for Red Hat systems that are not explicitly workstation
	return "server"
}

// cleanDirectory removes all contents of a directory but keeps the directory itself
func (service RedhatPatchService) cleanDirectory(dirPath string) error {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return fmt.Errorf("failed to read directory: %w", err)
	}

	for _, entry := range entries {
		entryPath := filepath.Join(dirPath, entry.Name())
		if err := os.RemoveAll(entryPath); err != nil {
			return fmt.Errorf("failed to remove %s: %w", entryPath, err)
		}
	}

	return nil
}

func (service RedhatPatchService) ExtractZipFile(zipFilePath, destDir string) error {
	// Open the zip file for reading
	reader, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return fmt.Errorf("failed to open zip file: %w", err)
	}
	defer func() {
		if closeErr := reader.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error closing zip reader: %s", closeErr.Error()))
		}
	}()

	// Extract all files and folders from the zip archive
	logger.ServiceLogger.Info(fmt.Sprintf("Found %d items in zip file to extract", len(reader.File)))
	for _, file := range reader.File {
		// Construct the full path for the extracted file
		extractPath := filepath.Join(destDir, file.Name)

		// Validate the extract path to prevent zip slip attacks
		cleanDestDir := filepath.Clean(destDir)
		cleanExtractPath := filepath.Clean(extractPath)
		if !strings.HasPrefix(cleanExtractPath, cleanDestDir+string(os.PathSeparator)) && cleanExtractPath != cleanDestDir {
			return fmt.Errorf("invalid file path in zip: %s", file.Name)
		}

		if file.FileInfo().IsDir() {
			// Create directory
			logger.ServiceLogger.Debug(fmt.Sprintf("Creating directory: %s", file.Name))
			if err := os.MkdirAll(extractPath, file.FileInfo().Mode()); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", extractPath, err)
			}
			continue
		}

		// Create the directories for the file
		if err := os.MkdirAll(filepath.Dir(extractPath), 0755); err != nil {
			return fmt.Errorf("failed to create directory for file %s: %w", extractPath, err)
		}

		// Extract the file
		logger.ServiceLogger.Debug(fmt.Sprintf("Extracting file: %s", file.Name))
		if err := service.extractFile(file, extractPath); err != nil {
			return fmt.Errorf("failed to extract file %s: %w", file.Name, err)
		}
	}

	return nil
}

// extractFile extracts a single file from the zip archive
func (service RedhatPatchService) extractFile(file *zip.File, extractPath string) error {
	// Open the file in the zip archive
	srcFile, err := file.Open()
	if err != nil {
		return fmt.Errorf("failed to open file in zip: %w", err)
	}
	defer func() {
		if closeErr := srcFile.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error closing source file: %s", closeErr.Error()))
		}
	}()

	// Create the destination file
	destFile, err := os.Create(extractPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %w", err)
	}
	defer func() {
		if closeErr := destFile.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error closing destination file: %s", closeErr.Error()))
		}
	}()

	// Copy the file content
	if _, err := io.Copy(destFile, srcFile); err != nil {
		return fmt.Errorf("failed to copy file content: %w", err)
	}

	// Set file permissions
	if err := os.Chmod(extractPath, file.FileInfo().Mode()); err != nil {
		logger.ServiceLogger.Warn(fmt.Sprintf("Failed to set file permissions for %s: %s", extractPath, err.Error()))
	}

	return nil
}

func (service RedhatPatchService) SyncRedHatPatch() error {
	logger.ServiceLogger.Info("Starting RedHat patch sync through agent nomination flow")

	// Read the linux-redhat-repo-sync-blocks.yml file content
	yamlFilePath := common.PrepareFilePath(common.FileDirectoryPath(), "patch-queries", "linux-redhat-repo-sync-blocks.yml")
	yamlContent, err := common.ReadYamlFile(yamlFilePath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error reading YAML file: %s", err.Error()))
		return fmt.Errorf("failed to read YAML file: %w", err)
	}

	// Extract queries from YAML content
	queries, ok := yamlContent["queries"].([]interface{})
	if !ok {
		logger.ServiceLogger.Error("Invalid YAML structure: queries not found")
		return fmt.Errorf("invalid YAML structure: queries not found")
	}

	// Process each query (Server and Workstation)
	for _, query := range queries {
		queryMap, ok := query.(map[string]interface{})
		if !ok {
			logger.ServiceLogger.Warn("Skipping invalid query entry")
			continue
		}

		// Extract blocks from the query
		blocks, ok := queryMap["blocks"].([]interface{})
		if !ok {
			logger.ServiceLogger.Warn("No blocks found in query")
			continue
		}

		// Process each block
		for _, block := range blocks {
			blockMap, ok := block.(map[string]interface{})
			if !ok {
				logger.ServiceLogger.Warn("Skipping invalid block entry")
				continue
			}

			if err := service.processBlock(blockMap); err != nil {
				logger.ServiceLogger.Error(fmt.Sprintf("Error processing block: %s", err.Error()))
				// Continue processing other blocks even if one fails
				continue
			}
		}
	}

	logger.ServiceLogger.Info("Completed RedHat patch sync through agent nomination flow")
	return nil
}

// processBlock processes a single repository block
func (service RedhatPatchService) processBlock(blockMap map[string]interface{}) error {
	// Extract block information
	folderPath, ok := blockMap["folderpath"].(string)
	if !ok {
		return fmt.Errorf("folderpath not found in block")
	}

	baseUrl, ok := blockMap["baseurl"].(string)
	if !ok {
		return fmt.Errorf("baseurl not found in block")
	}

	repoName, ok := blockMap["reponame"].(string)
	if !ok {
		return fmt.Errorf("reponame not found in block")
	}

	// Build the patch database file path
	patchDbFilePath := filepath.Join(common.FileDirectoryPath(), "patchmirror")
	logger.ServiceLogger.Debug(fmt.Sprintf("Patch Db Path File: %s", patchDbFilePath))

	// Append repodata to folder path
	folderPathWithRepoData := folderPath + string(filepath.Separator) + "repodata"

	// Get distribution and OS variant
	distribution := service.getDistribution(baseUrl)
	osVariant := service.getOsVariant(repoName)

	// Build final path
	finalPath := patchDbFilePath + folderPathWithRepoData
	logger.ServiceLogger.Debug(fmt.Sprintf("Final Redhat Folder Path: %s", finalPath))

	// Create RedhatContext
	context := NewRedhatContext(folderPathWithRepoData, baseUrl, distribution, osVariant, finalPath)

	// Process block data
	return service.processBlockData(context)
}

// GetDistribution extracts the RHEL distribution version from the file path
func (service RedhatPatchService) getDistribution(filePath string) string {
	filePath = strings.ToLower(filePath)

	if strings.Contains(filePath, "/7server/") || strings.Contains(filePath, "/7/7workstation") {
		return "RHEL_7"
	} else if strings.Contains(filePath, "/rhel8/") || strings.Contains(filePath, "/8/8workstation/") {
		return "RHEL_8"
	} else if strings.Contains(filePath, "/rhel9/") || strings.Contains(filePath, "/9/9workstation/") {
		return "RHEL_9"
	} else if strings.Contains(filePath, "/rhel10/") || strings.Contains(filePath, "/10/10workstation/") {
		return "RHEL_10"
	}

	return ""
}

// GetOsVariant extracts the OS variant (server/workstation) from the repository name
func (service RedhatPatchService) getOsVariant(repoName string) string {
	repoNameLower := strings.ToLower(repoName)

	if strings.Contains(repoNameLower, "server") {
		return "server"
	} else if strings.Contains(repoNameLower, "workstation") {
		return "workstation"
	}

	return ""
}

// processBlockData processes XML files in the repository directory
func (service RedhatPatchService) processBlockData(context *RedhatContext) error {
	logger.ServiceLogger.Debug(fmt.Sprintf("Processing block data for path: %s", context.FinalPath))

	// Check if directory exists
	if _, err := os.Stat(context.FinalPath); os.IsNotExist(err) {
		logger.ServiceLogger.Warn(fmt.Sprintf("Directory does not exist: %s", context.FinalPath))
		return nil // Not an error, just skip
	}

	// Find XML.gz files in the directory
	files, err := service.findXmlGzFiles(context.FinalPath)
	if err != nil {
		return fmt.Errorf("error finding XML.gz files: %w", err)
	}

	if len(files) == 0 {
		logger.ServiceLogger.Debug(fmt.Sprintf("No XML.gz files found in: %s", context.FinalPath))
		return nil
	}

	logger.ServiceLogger.Debug(fmt.Sprintf("Found %d XML.gz files", len(files)))

	// Process updateinfo XML files first
	for _, file := range files {
		if strings.HasSuffix(file.Name(), "-updateinfo.xml.gz") {
			if err := service.processUpdateInfoFile(file, context); err != nil {
				logger.ServiceLogger.Error(fmt.Sprintf("Error processing updateinfo file %s: %s", file.Name(), err.Error()))
				// Continue with other files
			}
		}
	}

	// Process primary XML files
	for _, file := range files {
		if strings.HasSuffix(file.Name(), "-primary.xml.gz") {
			if err := service.processPrimaryFile(file, context); err != nil {
				logger.ServiceLogger.Error(fmt.Sprintf("Error processing primary file %s: %s", file.Name(), err.Error()))
				// Continue with other files
			}
		}
	}

	return nil
}

// findXmlGzFiles finds all .xml.gz files in the specified directory
func (service RedhatPatchService) findXmlGzFiles(dirPath string) ([]os.DirEntry, error) {
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %w", dirPath, err)
	}

	var xmlGzFiles []os.DirEntry
	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".xml.gz") {
			xmlGzFiles = append(xmlGzFiles, entry)
		}
	}

	return xmlGzFiles, nil
}

// isFileAlreadyProcessed checks if a file has already been processed
func (service RedhatPatchService) isFileAlreadyProcessed(filePath string) bool {
	return service.fileSyncHistoryService.IsFileAlreadyProcessed(filePath)
}

// createFileSyncHistory creates a record of processed file
func (service RedhatPatchService) createFileSyncHistory(filePath string) error {
	return service.fileSyncHistoryService.CreateFileSyncHistory(filePath)
}

// processUpdateInfoFile processes an updateinfo XML.gz file
func (service RedhatPatchService) processUpdateInfoFile(file os.DirEntry, context *RedhatContext) error {
	filePath := filepath.Join(context.FinalPath, file.Name())

	// Check if file already processed
	if service.isFileAlreadyProcessed(filePath) {
		logger.ServiceLogger.Debug(fmt.Sprintf("File already processed: %s", filePath))
		return nil
	}

	logger.ServiceLogger.Debug(fmt.Sprintf("Processing updateinfo file: %s", file.Name()))

	// Extract the gzipped file to a temporary file
	tempFile, err := service.extractGzFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to extract gz file: %w", err)
	}
	defer func() {
		if err := os.Remove(tempFile); err != nil {
			logger.ServiceLogger.Warn(fmt.Sprintf("Failed to remove temp file %s: %s", tempFile, err.Error()))
		}
	}()

	// Process the extracted XML file
	if err := service.processRedHatUpdateXmlFile(tempFile); err != nil {
		return fmt.Errorf("failed to process update XML file: %w", err)
	}

	// Create file sync history
	if err := service.createFileSyncHistory(filePath); err != nil {
		logger.ServiceLogger.Warn(fmt.Sprintf("Failed to create file sync history: %s", err.Error()))
	}

	return nil
}

// processPrimaryFile processes a primary XML.gz file
func (service RedhatPatchService) processPrimaryFile(file os.DirEntry, context *RedhatContext) error {
	filePath := filepath.Join(context.FinalPath, file.Name())

	// Check if file already processed
	if service.isFileAlreadyProcessed(filePath) {
		logger.ServiceLogger.Debug(fmt.Sprintf("File already processed: %s", filePath))
		return nil
	}

	logger.ServiceLogger.Debug(fmt.Sprintf("Processing primary file: %s", file.Name()))

	// Extract the gzipped file to a temporary file
	tempFile, err := service.extractGzFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to extract gz file: %w", err)
	}
	defer func() {
		if err := os.Remove(tempFile); err != nil {
			logger.ServiceLogger.Warn(fmt.Sprintf("Failed to remove temp file %s: %s", tempFile, err.Error()))
		}
	}()

	// Process the extracted XML file
	if err := service.processRedHatPrimaryXmlFile(tempFile, context); err != nil {
		return fmt.Errorf("failed to process primary XML file: %w", err)
	}

	// Create file sync history
	if err := service.createFileSyncHistory(filePath); err != nil {
		logger.ServiceLogger.Warn(fmt.Sprintf("Failed to create file sync history: %s", err.Error()))
	}

	return nil
}

// extractGzFile extracts a gzipped file to a temporary file and returns the temp file path
func (service RedhatPatchService) extractGzFile(gzFilePath string) (string, error) {
	// Open the gzipped file
	gzFile, err := os.Open(gzFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to open gz file: %w", err)
	}
	defer func() {
		if closeErr := gzFile.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error closing gz file: %s", closeErr.Error()))
		}
	}()

	// Create gzip reader
	gzReader, err := gzip.NewReader(gzFile)
	if err != nil {
		return "", fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer func() {
		if closeErr := gzReader.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error closing gzip reader: %s", closeErr.Error()))
		}
	}()

	// Create temporary file
	tempFile, err := os.CreateTemp("", "redhat-*.xml")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %w", err)
	}
	defer func() {
		if closeErr := tempFile.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error closing temp file: %s", closeErr.Error()))
		}
	}()

	// Copy decompressed content to temp file
	if _, err := io.Copy(tempFile, gzReader); err != nil {
		os.Remove(tempFile.Name()) // Clean up on error
		return "", fmt.Errorf("failed to copy decompressed content: %w", err)
	}

	logger.ServiceLogger.Debug(fmt.Sprintf("Extracted gz file to: %s", tempFile.Name()))
	return tempFile.Name(), nil
}

// processRedHatUpdateXmlFile processes the extracted updateinfo XML file
func (service RedhatPatchService) processRedHatUpdateXmlFile(xmlFilePath string) error {
	return service.xmlParser.ParseUpdateInfoXml(xmlFilePath)
}

// processRedHatPrimaryXmlFile processes the extracted primary XML file
func (service RedhatPatchService) processRedHatPrimaryXmlFile(xmlFilePath string, context *RedhatContext) error {
	return service.xmlParser.ParsePrimaryXml(xmlFilePath, context)
}

// TestSyncRedHatPatchThroughAgentNominationFlow tests the complete sync flow with existing files
func (service RedhatPatchService) TestSyncRedHatPatchThroughAgentNominationFlow() error {
	logger.ServiceLogger.Info("Starting test of RedHat patch sync through agent nomination flow")

	// Test with a specific existing directory
	testFolderPath := "/redhat/10/x86_64/appstream/os/repodata"
	testBaseUrl := "https://cdn.redhat.com/content/dist/rhel10/10/x86_64/appstream/os"
	testRepoName := "[rhel-10-server-x86_64-appstream-rpms-ziro-cache]"

	// Build the patch database file path
	patchDbFilePath := filepath.Join(common.FileDirectoryPath(), "patchmirror")
	logger.ServiceLogger.Debug(fmt.Sprintf("Patch Db Path File: %s", patchDbFilePath))

	// Build final path
	finalPath := patchDbFilePath + testFolderPath
	logger.ServiceLogger.Debug(fmt.Sprintf("Final Redhat Folder Path: %s", finalPath))

	// Get distribution and OS variant
	distribution := service.getDistribution(testBaseUrl)
	osVariant := service.getOsVariant(testRepoName)

	// Create RedhatContext
	context := NewRedhatContext(testFolderPath, testBaseUrl, distribution, osVariant, finalPath)

	// Test processing block data
	err := service.processBlockData(context)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error in test processing: %s", err.Error()))
		return err
	}

	logger.ServiceLogger.Info("Test completed successfully")
	return nil
}

func (service RedhatPatchService) RetrieveCertificateFilePath(uuid string) (string, string) {
	clientCrtFilePath := ""
	clientKeyFilePath := ""
	osVariant := strings.Split(uuid, "-")[len(strings.Split(uuid, "-"))-1]
	certificateDir := filepath.Join(common.FileDirectoryPath(), "certificates", "redhat", osVariant)
	_, err := os.Stat(certificateDir)
	logger.ServiceLogger.Debug("certificateDir: ", certificateDir)
	if !os.IsExist(err) {
		zipFileList, err := os.ReadDir(certificateDir)
		if err == nil {
			for _, zipFile := range zipFileList {
				if strings.HasSuffix(zipFile.Name(), ".zip") {
					zipFilePath := filepath.Join(certificateDir, zipFile.Name())
					err := service.ExtractZipFile(zipFilePath, certificateDir)
					if err != nil {
						logger.ServiceLogger.Error("[DownloadFileInMainServer]: ", err)
					}
					break
				}
			}
			certFileList, err := os.ReadDir(certificateDir)
			if err == nil && len(certFileList) > 0 {
				var keyFilePath, certFilePath string
				for _, certFile := range certFileList {
					if strings.HasSuffix(certFile.Name(), ".pem") {
						if strings.Contains(certFile.Name(), "clientkey") {
							keyFilePath = filepath.Join(certificateDir, certFile.Name())
						} else if strings.Contains(certFile.Name(), "clientcert") {
							certFilePath = filepath.Join(certificateDir, certFile.Name())
						}
					}
				}
				if keyFilePath != "" && certFilePath != "" {
					err := os.Chmod(keyFilePath, 0600)
					if err != nil {
						return "", ""
					}

					err = os.Chmod(certFilePath, 0600)
					if err != nil {
						return "", ""
					}
					clientCrtFilePath = certFilePath

					clientKeyFilePath = keyFilePath
				}
			} else {
				logger.ServiceLogger.Error("[DownloadFileInMainServer]: certFileList not found, ", err)
			}
		}
	}
	return clientCrtFilePath, clientKeyFilePath
}
