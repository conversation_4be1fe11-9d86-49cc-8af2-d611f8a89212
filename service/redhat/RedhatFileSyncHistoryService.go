package redhat

import (
	"deployment/common"
	"deployment/converter"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
)

type RedhatFileSyncHistoryService struct {
	Repository *repository.RedhatFileSyncHistoryRepository
}

func NewRedhatFileSyncHistoryService() *RedhatFileSyncHistoryService {
	return &RedhatFileSyncHistoryService{
		Repository: repository.NewRedhatFileSyncHistoryRepository(),
	}
}

func (service RedhatFileSyncHistoryService) GetRedhatFileSyncHistory(id int64) (rest.RedhatFileSyncHistoryRest, error) {
	history, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.RedhatFileSyncHistoryRest{}, err
	}
	return service.convertToRest(history), nil
}

func (service RedhatFileSyncHistoryService) Create(historyRest rest.RedhatFileSyncHistoryRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Create RedhatFileSyncHistory process started")

	// Convert rest to model
	history := service.convertToModel(historyRest)

	// Call repository create method
	id, err := service.Repository.Create(&history)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating RedhatFileSyncHistory: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create RedhatFileSyncHistory process completed successfully")
	return id, common.CustomError{}
}

// IsFileAlreadyProcessed checks if a file has already been processed
func (service RedhatFileSyncHistoryService) IsFileAlreadyProcessed(xmlName string) bool {
	exists, err := service.Repository.ExistsByXmlName(xmlName)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error checking if file already processed: %s", err.Error()))
		return false
	}
	return exists
}

// CreateFileSyncHistory creates a new file sync history record
func (service RedhatFileSyncHistoryService) CreateFileSyncHistory(xmlName string) error {
	historyRest := rest.RedhatFileSyncHistoryRest{
		BaseEntityRest: rest.BaseEntityRest{
			CreatedTime: common.CurrentMillisecond(),
			UpdatedTime: common.CurrentMillisecond(),
			CreatedById: 0, // System user
			UpdatedById: 0, // System user
			OOB:         true,
			Removed:     false,
		},
		XmlName:  xmlName,
		ScanTime: common.CurrentMillisecond(),
	}

	_, err := service.Create(historyRest)
	if err.Message != "" {
		return fmt.Errorf("failed to create file sync history: %s", err.Message)
	}

	return nil
}

// Helper methods for conversion

func (service RedhatFileSyncHistoryService) convertToRest(history model.RedhatFileSyncHistory) rest.RedhatFileSyncHistoryRest {
	baseRest := converter.ConvertToBaseEntityRest(history.BaseEntityModel)
	return rest.RedhatFileSyncHistoryRest{
		BaseEntityRest: baseRest,
		XmlName:        history.XmlName,
		ScanTime:       history.ScanTime,
	}
}

func (service RedhatFileSyncHistoryService) convertToModel(historyRest rest.RedhatFileSyncHistoryRest) model.RedhatFileSyncHistory {
	baseModel := converter.ConvertToBaseEntityModel(historyRest.BaseEntityRest)
	return model.RedhatFileSyncHistory{
		BaseEntityModel: baseModel,
		XmlName:         historyRest.XmlName,
		ScanTime:        historyRest.ScanTime,
	}
}
