package redhat

import (
	"deployment/common"
	"deployment/converter"
	"deployment/interfaces"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"fmt"
	"net/http"
	"strconv"
	"strings"
)

type RedhatAgentNominationService struct {
	Repository *repository.RedhatAgentNominationRepository
}

func NewRedhatAgentNominationService() *RedhatAgentNominationService {
	return &RedhatAgentNominationService{
		Repository: repository.NewRedhatAgentNominationRepository(),
	}
}

func (service RedhatAgentNominationService) GetRedhatAgentNomination(id int64) (rest.RedhatAgentNominationRest, error) {
	nomination, err := service.Repository.GetById(id, false)
	if err != nil {
		return rest.RedhatAgentNominationRest{}, err
	}
	return service.convertToRest(nomination), nil
}

func (service RedhatAgentNominationService) CreateRedhatAgentNomination(nominationRest rest.RedhatAgentNominationRest) (int64, common.CustomError) {
	logger.ServiceLogger.Info("Process started to create RedhatAgentNomination")

	// Handle base entity fields
	if nominationRest.CreatedTime == 0 {
		nominationRest.CreatedTime = common.CurrentMillisecond()
	}
	nominationRest.CreatedById = common.GetUserFromCallContext()
	nominationRest.UpdatedTime = nominationRest.CreatedTime
	nominationRest.UpdatedById = nominationRest.CreatedById

	// Set default status if not provided
	if nominationRest.Status == "" {
		nominationRest.Status = "pending"
	}

	// Convert rest to model
	nomination := service.convertToModel(nominationRest)

	// Call repository create method
	id, err := service.Repository.Create(&nomination)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while creating RedhatAgentNomination: %s", err.Error()))
		return 0, common.CustomError{Message: err.Error()}
	}

	logger.ServiceLogger.Info("Create RedhatAgentNomination process completed successfully")
	return id, common.CustomError{}
}

func (service RedhatAgentNominationService) UpdateRedhatAgentNomination(id int64, nominationRest rest.RedhatAgentNominationRest) (bool, common.CustomError) {
	logger.ServiceLogger.Info(fmt.Sprintf("Process started to update RedhatAgentNomination with id - %v", id))

	// Retrieve existing nomination
	nomination, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while updating RedhatAgentNomination for id - %v, Error: %s", id, err.Error()))
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	// Perform partial update - only allow assetId updates via API
	diffMap, isUpdatable := service.performPartialUpdate(&nomination, nominationRest)
	if isUpdatable {
		// Handle base entity fields
		if nominationRest.UpdatedTime == 0 {
			nomination.UpdatedTime = common.CurrentMillisecond()
		} else {
			nomination.UpdatedTime = nominationRest.UpdatedTime
		}
		nomination.UpdatedById = common.GetUserFromCallContext()

		// Call repository update method
		_, err := service.Repository.Update(&nomination)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error while updating RedhatAgentNomination for id - %v, Error: %s", id, err.Error()))
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}

		go service.AfterUpdate(diffMap, nomination)
		logger.ServiceLogger.Info(fmt.Sprintf("Update RedhatAgentNomination process completed successfully for id - %v", id))
		return true, common.CustomError{}
	} else {
		return false, common.CustomError{}
	}
}

func (service RedhatAgentNominationService) GetAllRedhatAgentNomination(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	countQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.REDHAT_AGENT_NOMINATION.String(), true, "")
	var responsePage rest.ListResponseRest
	var nominations []model.RedhatAgentNomination
	var err error

	// Count total records
	count, _ := service.Repository.CountByQuery(countQuery.Query, countQuery.Parameters)
	if count > 0 {
		// Fetch data based on search filter
		searchQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.REDHAT_AGENT_NOMINATION.String(), false, "")
		nominations, err = service.Repository.GetAllByQuery(searchQuery.Query, searchQuery.Parameters)
		if err != nil {
			return responsePage, err
		}
		// Convert nomination list to rest list
		responsePage.ObjectList = service.convertListToRest(nominations)
	}
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service RedhatAgentNominationService) CreateDefaultNominations() {
	logger.ServiceLogger.Info("Creating default RedhatAgentNomination entries if they don't exist")

	// Check and create SERVER nomination
	serverExists, err := service.Repository.ExistsByRedhatOsVariant(model.SERVER)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error checking for SERVER nomination: %s", err.Error()))
		return
	}

	if !serverExists {
		serverNomination := model.RedhatAgentNomination{
			BaseEntityModel: model.BaseEntityModel{
				Name:        "",
				CreatedTime: common.CurrentMillisecond(),
				UpdatedTime: common.CurrentMillisecond(),
				CreatedById: 0, // System user
				UpdatedById: 0, // System user
				OOB:         true,
				Removed:     false,
			},
			LastSyncTime:     0,
			AssetId:          0,
			LastRepoSyncDate: 0,
			RedhatOsVariant:  model.SERVER,
			Status:           model.NOMINATION_PENDING,
			Schedule: model.PatchSchedule{
				Type:  model.DAILY,
				Value: 0, // Default value for daily schedule
			},
		}

		_, err := service.Repository.Create(&serverNomination)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error creating SERVER nomination: %s", err.Error()))
			return
		}
		logger.ServiceLogger.Info("Created default SERVER nomination")
	}

	// Check and create WORKSTATION nomination
	workstationExists, err := service.Repository.ExistsByRedhatOsVariant(model.WORKSTATION)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error checking for WORKSTATION nomination: %s", err.Error()))
		return
	}

	if !workstationExists {
		workstationNomination := model.RedhatAgentNomination{
			BaseEntityModel: model.BaseEntityModel{
				Name:        "",
				CreatedTime: common.CurrentMillisecond(),
				UpdatedTime: common.CurrentMillisecond(),
				CreatedById: 0, // System user
				UpdatedById: 0, // System user
				OOB:         true,
				Removed:     false,
			},
			LastSyncTime:     0,
			AssetId:          0,
			LastRepoSyncDate: 0,
			RedhatOsVariant:  model.WORKSTATION,
			Status:           model.NOMINATION_PENDING,
		}

		_, err := service.Repository.Create(&workstationNomination)
		if err != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("Error creating WORKSTATION nomination: %s", err.Error()))
			return
		}
		logger.ServiceLogger.Info("Created default WORKSTATION nomination")
	}

	logger.ServiceLogger.Info("Default RedhatAgentNomination entries creation completed")
	return
}

// Helper methods for conversion and partial updates

func (service RedhatAgentNominationService) convertToRest(nomination model.RedhatAgentNomination) rest.RedhatAgentNominationRest {
	baseRest := converter.ConvertToBaseEntityRest(nomination.BaseEntityModel)
	return rest.RedhatAgentNominationRest{
		BaseEntityRest:   baseRest,
		LastSyncTime:     nomination.LastSyncTime,
		AssetId:          nomination.AssetId,
		LastRepoSyncDate: nomination.LastRepoSyncDate,
		RedhatOsVariant:  nomination.RedhatOsVariant.String(),
		Status:           nomination.Status.String(),
		Schedule:         service.convertToScheduleRest(nomination.Schedule),
	}
}

func (service RedhatAgentNominationService) convertToModel(nominationRest rest.RedhatAgentNominationRest) model.RedhatAgentNomination {
	baseModel := converter.ConvertToBaseEntityModel(nominationRest.BaseEntityRest)
	var osVariant model.RedhatOsVariant
	var status model.RedhatAgentNominationStatus
	return model.RedhatAgentNomination{
		BaseEntityModel:  baseModel,
		LastSyncTime:     nominationRest.LastSyncTime,
		AssetId:          nominationRest.AssetId,
		LastRepoSyncDate: nominationRest.LastRepoSyncDate,
		RedhatOsVariant:  osVariant.ToVariant(nominationRest.RedhatOsVariant),
		Status:           status.ToStatus(nominationRest.Status),
		Schedule:         service.convertToScheduleModel(nominationRest.Schedule),
	}
}

func (service RedhatAgentNominationService) convertToScheduleModel(rest rest.PatchScheduleRest) model.PatchSchedule {
	return model.PatchSchedule{
		Type:  rest.Type,
		Value: rest.Value,
	}
}

func (service RedhatAgentNominationService) convertToScheduleRest(model model.PatchSchedule) rest.PatchScheduleRest {
	return rest.PatchScheduleRest{
		Type:  model.Type,
		Value: model.Value,
	}
}

func (service RedhatAgentNominationService) convertListToRest(nominations []model.RedhatAgentNomination) []rest.RedhatAgentNominationRest {
	var nominationRests []rest.RedhatAgentNominationRest
	for _, nomination := range nominations {
		nominationRests = append(nominationRests, service.convertToRest(nomination))
	}
	return nominationRests
}

func (service RedhatAgentNominationService) performPartialUpdate(nomination *model.RedhatAgentNomination, nominationRest rest.RedhatAgentNominationRest) (map[string]map[string]interface{}, bool) {
	diffMap := map[string]map[string]interface{}{}
	isUpdatable := false

	// Allow assetId updates via API
	if nominationRest.PatchMap["assetId"] != nil && nomination.AssetId != nominationRest.AssetId {
		common.PrepareInDiffMap("asset_id", nomination.AssetId, nominationRest.AssetId, &diffMap)
		nomination.AssetId = nominationRest.AssetId
		isUpdatable = true
	}

	// Allow status updates via API
	if nominationRest.PatchMap["status"] != nil {
		var status model.RedhatAgentNominationStatus
		newStatus := status.ToStatus(nominationRest.Status)
		if nomination.Status != newStatus {
			common.PrepareInDiffMap("status", nomination.Status.String(), newStatus.String(), &diffMap)
			nomination.Status = newStatus
			isUpdatable = true
		}
	}

	// Allow schedule updates via API
	if nominationRest.PatchMap["schedule"] != nil {
		newSchedule := service.convertToScheduleModel(nominationRest.Schedule)
		if nomination.Schedule.Type != newSchedule.Type || nomination.Schedule.Value != newSchedule.Value {
			oldScheduleStr := fmt.Sprintf("Type: %s, Value: %d", nomination.Schedule.Type.String(), nomination.Schedule.Value)
			newScheduleStr := fmt.Sprintf("Type: %s, Value: %d", newSchedule.Type.String(), newSchedule.Value)
			common.PrepareInDiffMap("schedule", oldScheduleStr, newScheduleStr, &diffMap)
			nomination.Schedule = newSchedule
			isUpdatable = true
		}
	}

	return diffMap, isUpdatable
}

// createRedhatAgentNominationTask creates an agent task for REDHAT_AGENT_NOMINATION
func (service RedhatAgentNominationService) createRedhatAgentNominationTask(nomination model.RedhatAgentNomination, assetIdDiff map[string]interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while creating REDHAT_AGENT_NOMINATION task for nomination id:", nomination.Id, err)
		}
	}()

	// Read the linux-redhat-repo-sync-blocks.yml file content
	yamlFilePath := common.PrepareFilePath(common.FileDirectoryPath(), "patch-queries", "linux-redhat-repo-sync-blocks.yml")
	yamlContent, err := common.ReadYamlFile(yamlFilePath)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error reading YAML file for REDHAT_AGENT_NOMINATION task creation for nomination %d: %s", nomination.Id, err.Error()))
		return
	}

	// Get asset details to determine OS variant
	agentService := interfaces.GetAgentService()
	if agentService == nil {
		logger.ServiceLogger.Error("AgentService not available for REDHAT_AGENT_NOMINATION task creation")
		return
	}
	assetDetails, _ := agentService.GetAsset(strconv.FormatInt(nomination.AssetId, 10))

	var osVariant = "server" // default to server
	if assetDetails != nil {
		platform, ok := assetDetails["platform_version"].(string)
		if ok && strings.Contains(platform, "Red Hat") {
			if strings.Contains(strings.ToLower(platform), "workstation") {
				osVariant = "workstation"
			} else {
				osVariant = "server"
			}
		}
	}

	// Filter YAML content by OS variant
	filteredContent := filterRepoSyncBlocksByOsVariant(yamlContent, osVariant)

	// Create custom task details
	customTaskDetails := map[string]interface{}{
		"repoSyncBlocks": filteredContent,
		"osVariant":      osVariant,
		"nominationId":   nomination.Id,
		"assetIdChange":  assetIdDiff,
	}

	// Create agent task
	agentTask := rest.AgentTaskRest{
		BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
			RefId:    nomination.Id,
			RefModel: common.REDHAT_AGENT_NOMINATION.String(),
		},
		TaskType:          model.REDHAT_AGENT_NOMINATION.String(),
		AgentId:           nomination.AssetId,
		TaskStatus:        model.TaskReadyToDeploy.String(),
		CustomTaskDetails: customTaskDetails,
	}

	agentTaskService := interfaces.GetAgentTaskService()
	if agentTaskService == nil {
		logger.ServiceLogger.Error("AgentTaskService not available for REDHAT_AGENT_NOMINATION task creation")
		return
	}
	taskId, err := agentTaskService.Create(agentTask)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error creating REDHAT_AGENT_NOMINATION task for nomination %d: %s", nomination.Id, err.Error()))
	} else {
		logger.ServiceLogger.Info(fmt.Sprintf("Successfully created REDHAT_AGENT_NOMINATION task %d for nomination %d with asset %d", taskId, nomination.Id, nomination.AssetId))
	}
}

// filterRepoSyncBlocksByOsVariant filters the YAML content by OS variant
func filterRepoSyncBlocksByOsVariant(yamlContent map[string]interface{}, osVariant string) map[string]interface{} {
	filteredContent := map[string]interface{}{}

	if queries, ok := yamlContent["queries"].([]interface{}); ok {
		for _, query := range queries {
			if queryMap, ok := query.(map[string]interface{}); ok {
				if name, ok := queryMap["name"].(string); ok {
					// Match the OS variant with the query name (case-insensitive)
					if strings.EqualFold(name, osVariant) {
						filteredContent = queryMap
						break
					}
				}
			}
		}
	}

	return filteredContent
}

func (service RedhatAgentNominationService) AfterUpdate(diffMap map[string]map[string]interface{}, nomination model.RedhatAgentNomination) {
	if len(diffMap) > 0 {
		// Audit creation removed as per requirement

		// Check if asset_id was updated and create task
		if assetIdDiff, exists := diffMap["asset_id"]; exists && nomination.AssetId > 0 {
			logger.ServiceLogger.Info(fmt.Sprintf("Asset ID updated for nomination %d, creating REDHAT_AGENT_NOMINATION task", nomination.Id))
			service.createRedhatAgentNominationTask(nomination, assetIdDiff)
		}
	}
}
