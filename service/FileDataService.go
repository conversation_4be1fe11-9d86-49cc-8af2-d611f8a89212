package service

import (
	"crypto/tls"
	"crypto/x509"
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	_ "encoding/pem"
	_ "golang.org/x/crypto/pkcs12"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

type FileDataService struct {
	repository *repository.FileDataRepository
}

func NewFileDataService() *FileDataService {
	return &FileDataService{
		repository: repository.NewFileDataRepository(),
	}
}

func (service FileDataService) convertToModel(rest rest.FileDataRest) *model.FileData {
	if rest.BaseEntityRest.Name == "" {
		rest.BaseEntityRest.Name = rest.RefName
	}
	return &model.FileData{
		BaseEntityModel: ConvertToBaseEntityModel(rest.BaseEntityRest),
		RealName:        rest.RealName,
		RefName:         rest.RefName,
		CheckSum:        rest.CheckSum,
		Size:            rest.Size,
	}
}

func (service FileDataService) convertToRest(model model.FileData) rest.FileDataRest {
	return rest.FileDataRest{
		BaseEntityRest: ConvertToBaseEntityRest(model.BaseEntityModel),
		RealName:       model.RealName,
		RefName:        model.RefName,
		CheckSum:       model.CheckSum,
		Size:           model.Size,
	}
}

func (service FileDataService) CreateFileData(restFileData rest.FileDataRest) (rest.FileDataRest, error) {
	fileData := service.convertToModel(restFileData)
	fileData.CreatedById = common.GetUserFromCallContext()
	fileData.CreatedTime = common.CurrentMillisecond()
	data, _ := service.GetFileDataByFileRefName(fileData.RefName)
	if data.Id != 0 {
		return data, nil
	}
	id, err := service.repository.Create(fileData)
	if err != nil {
		return restFileData, err
	}
	fileDataModel, err := service.repository.GetById(id)
	if err != nil {
		return restFileData, err
	}
	restModel := service.convertToRest(fileDataModel)
	return restModel, nil
}

func (service FileDataService) GetFileDataByFileRefName(refName string) (rest.FileDataRest, error) {
	fileData, err := service.repository.GetByRefName(refName)
	if err != nil {
		return rest.FileDataRest{}, err
	}
	restModel := service.convertToRest(fileData)
	return restModel, nil
}
func (service FileDataService) DownloadFileFromURL(fileUrl, filename, fileType, folder string) (rest.FileDataRest, common.CustomError) {
	return service.DownloadFileFromURLByCert(fileUrl, filename, fileType, folder, "", "")
}

func (service FileDataService) DownloadFileFromURLByCert(fileUrl, filename, fileType, folder, clientCertPath, clientKeyPath string) (rest.FileDataRest, common.CustomError) {
	originalFileName := ""

	parsedURL, err := url.Parse(fileUrl)
	if err != nil {
		customErr := common.Error(err.Error(), http.StatusInternalServerError)
		return rest.FileDataRest{}, customErr
	}

	var response *http.Response
	if clientCertPath != "" && clientKeyPath != "" {
		certPEM, err := os.ReadFile(clientCertPath)
		if err != nil {
			return rest.FileDataRest{}, common.Error("failed to read client cert: "+err.Error(), http.StatusInternalServerError)
		}

		keyPEM, err := os.ReadFile(clientKeyPath)
		if err != nil {
			return rest.FileDataRest{}, common.Error("failed to read client key: "+err.Error(), http.StatusInternalServerError)
		}

		// Load client certificate
		tlsCert, err := tls.X509KeyPair(certPEM, keyPEM)
		if err != nil {
			customErr := common.Error(err.Error(), http.StatusInternalServerError)
			return rest.FileDataRest{}, customErr
		}

		// Create CA pool - using system root CAs since we don't have caCerts from pkcs12.Decode
		caPool, err := x509.SystemCertPool()
		if err != nil {
			caPool = x509.NewCertPool()
		}

		// Setup HTTPS client
		tlsConfig := &tls.Config{
			Certificates: []tls.Certificate{tlsCert},
			RootCAs:      caPool,
		}
		tlsConfig.BuildNameToCertificate()

		transport := &http.Transport{TLSClientConfig: tlsConfig}
		client := &http.Client{Transport: transport}
		response, err = client.Get(fileUrl)
		if err != nil {
			customErr := common.Error(err.Error(), http.StatusInternalServerError)
			return rest.FileDataRest{}, customErr
		}

	} else {
		response, err = http.Get(fileUrl)
		if err != nil {
			customErr := common.Error(err.Error(), http.StatusInternalServerError)
			return rest.FileDataRest{}, customErr
		}
	}
	defer func(Body io.ReadCloser) {
		err = Body.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFileFromURL]", err)
		}
	}(response.Body)

	if response.StatusCode != http.StatusOK {
		return rest.FileDataRest{}, common.Error("Incompatible file type at file path", http.StatusInternalServerError)
	}

	if "" != strings.Trim(fileType, " ") {
		originalFileName = filepath.Base(parsedURL.Path)
	}

	if originalFileName == "" {
		originalFileName = "dummy-" + string(rand.Intn(100)) + "." + fileType
	}
	destination := common.FileDirectoryPath()
	if folder == "" {
		destination = filepath.Join(destination, filename)
	} else {
		destination = filepath.Join(folder, filename)
	}
	file, err := os.Create(destination)
	if err != nil {
		return rest.FileDataRest{}, common.Error(err.Error(), http.StatusInternalServerError)
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFileFromURL]", err)
		}
	}(file)

	fileSize, err := io.Copy(file, response.Body)
	if err != nil {
		return rest.FileDataRest{}, common.Error(err.Error(), http.StatusInternalServerError)
	}

	checksum, err := common.GenerateFileCheckSum(file)
	if err != nil {
		return rest.FileDataRest{}, common.Error(err.Error(), http.StatusInternalServerError)
	}

	fileDataRest := rest.FileDataRest{
		RefName:  filename,
		RealName: originalFileName,
		Size:     fileSize,
		CheckSum: checksum,
	}

	logger.ServiceLogger.Info("File downloaded successfully to: %s\n", destination)
	return fileDataRest, common.CustomError{}
}

func (service FileDataService) DownloadFileFromSMBPath(fileRefName, smbFilePath string) (rest.FileDataRest, common.CustomError) {
	smbPath := strings.Split(smbFilePath, "/")
	smbPathLength := len(smbPath)
	originalFileName := smbPath[smbPathLength-1]
	smbFilePath = strings.Replace(smbFilePath, originalFileName, "", 1)
	smbCommand := "smbclient '" + smbFilePath + "' -c 'lcd /tmp; get " + originalFileName + "' -N"
	cmd := exec.Command("sh", "-c", smbCommand)

	_, err := cmd.CombinedOutput()
	if err != nil {
		customError := common.Error("Error while downloading file from smb  : "+err.Error(), http.StatusInternalServerError)
		return rest.FileDataRest{}, customError
	}

	downloadedFilePath := "/tmp/" + originalFileName
	downloadedFile, err := os.Open(downloadedFilePath)
	if err != nil {
		customError := common.Error("Error while downloading file from smb  : "+err.Error(), http.StatusInternalServerError)
		return rest.FileDataRest{}, customError
	}
	defer func(downloadedFile *os.File) {
		err = downloadedFile.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFileFromURL]", err)
		}
	}(downloadedFile)

	destination := filepath.Join(common.FileDirectoryPath(), fileRefName)
	dest, err := os.Create(destination)
	if err != nil {
		customError := common.Error("Error while downloading file from smb  : "+err.Error(), http.StatusInternalServerError)
		return rest.FileDataRest{}, customError
	}
	defer func(dest *os.File) {
		err = dest.Close()
		if err != nil {
			logger.ServiceLogger.Error("[DownloadFileFromURL]", err)
		}
	}(dest)

	fileSize, err := io.Copy(dest, downloadedFile)
	if err != nil {
		return rest.FileDataRest{}, common.Error(err.Error(), http.StatusInternalServerError)
	}

	checksum, err := common.GenerateFileCheckSum(downloadedFile)
	if err != nil {
		return rest.FileDataRest{}, common.Error(err.Error(), http.StatusInternalServerError)
	}

	err = os.Remove(downloadedFilePath)
	if err != nil {
		logger.ServiceLogger.Error("[DownloadFileFromURL]", err)
	}

	fileDataRest := rest.FileDataRest{
		RefName:  fileRefName,
		RealName: originalFileName,
		Size:     fileSize,
		CheckSum: checksum,
	}
	return fileDataRest, common.CustomError{}
}
