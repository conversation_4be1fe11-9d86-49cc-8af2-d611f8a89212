package service

import (
	"deployment/cache"
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/repository"
	"deployment/rest"
	"deployment/service/redhat"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"reflect"
	"strings"
	"time"
)

type DeploymentService struct {
	Repository *repository.DeploymentRepository
}

func NewDeploymentService() *DeploymentService {
	return &DeploymentService{
		Repository: repository.NewDeploymentRepository(),
	}
}

func (service DeploymentService) convertToModel(deploymentRest rest.DeploymentRest) (*model.Deployment, error) {
	var deploymentType model.DeploymentType
	var deploymentStage model.DeploymentStage
	var origin model.DeploymentOrigin
	var err error

	deploymentType, err = deploymentType.ToDeploymentType(deploymentRest.DeploymentType)
	if err != nil {
		return &model.Deployment{}, err
	}

	deploymentStage = deploymentStage.ToDeploymentStage(deploymentRest.DeploymentStage)

	origin = origin.ToDeploymentOrigin(deploymentRest.Origin)

	deploymentRest.Name = deploymentRest.DisplayName
	return &model.Deployment{
		BaseEntityRefModel:      ConvertToBaseEntityRefModel(deploymentRest.BaseEntityRefModelRest),
		AgentScopeFilter:        ConvertToAgentScopeFilter(deploymentRest.AgentScopeFilterRest),
		DisplayName:             deploymentRest.DisplayName,
		Description:             deploymentRest.Description,
		DeploymentType:          deploymentType,
		DeploymentStage:         deploymentStage,
		RefIds:                  deploymentRest.RefIds,
		ComputerIds:             deploymentRest.ComputerIds,
		ComputerGroupIds:        deploymentRest.ComputerGroupIds,
		DeploymentPolicyId:      deploymentRest.DeploymentPolicyId,
		NotifyEmailIds:          deploymentRest.NotifyEmailIds,
		RetryCount:              deploymentRest.RetryCount,
		IsPkgSelectAsBundle:     deploymentRest.IsPkgSelectAsBundle,
		IsSelfServiceDeployment: deploymentRest.IsSelfServiceDeployment,
		NextExecutionTime:       deploymentRest.NextExecutionTime,
		LastExecutionTime:       deploymentRest.LastExecutionTime,
		StartTime:               deploymentRest.StartTime,
		Origin:                  origin,
	}, nil
}

func (service DeploymentService) convertToRest(deploymentInfo model.Deployment) rest.DeploymentRest {
	completedTaskCount := 0
	successTaskCount := 0
	failedTaskCount := 0
	totalTaskCount := 0
	if deploymentInfo.Id > 0 {
		allDeploymentTasks, _ := NewAgentTaskService().Repository.GetByDeploymentId(deploymentInfo.Id)
		if len(allDeploymentTasks) > 0 {
			for _, task := range allDeploymentTasks {
				if task.TaskStatus == model.TaskSuccess || task.TaskStatus == model.TaskFailed || task.TaskStatus == model.TaskCancelled {
					completedTaskCount++
				}
				if task.TaskStatus == model.TaskSuccess {
					successTaskCount++
				}
				if task.TaskStatus == model.TaskFailed {
					failedTaskCount++
				}
			}

		}
		totalTaskCount = len(allDeploymentTasks)
	}

	isRecurring := false
	policy, err := NewDeploymentPolicyService().Repository.GetById(deploymentInfo.Id, false)
	isRecurring = err == nil && policy.InitiateDeploymentOn == model.Recurring && !strings.EqualFold(deploymentInfo.RefModel, common.COMPLIANCES.String())

	deploymentRest := rest.DeploymentRest{
		BaseEntityRefModelRest:  ConvertToBaseEntityRefModelRest(deploymentInfo.BaseEntityRefModel),
		AgentScopeFilterRest:    ConvertToAgentScopeFilterRest(deploymentInfo.AgentScopeFilter),
		DisplayName:             deploymentInfo.DisplayName,
		Description:             deploymentInfo.Description,
		DeploymentType:          deploymentInfo.DeploymentType.String(),
		DeploymentStage:         deploymentInfo.DeploymentStage.String(),
		RefIds:                  deploymentInfo.RefIds,
		ComputerIds:             deploymentInfo.ComputerIds,
		ComputerGroupIds:        deploymentInfo.ComputerGroupIds,
		DeploymentPolicyId:      deploymentInfo.DeploymentPolicyId,
		NotifyEmailIds:          deploymentInfo.NotifyEmailIds,
		RetryCount:              deploymentInfo.RetryCount,
		IsPkgSelectAsBundle:     deploymentInfo.IsPkgSelectAsBundle,
		IsSelfServiceDeployment: deploymentInfo.IsSelfServiceDeployment,
		NextExecutionTime:       deploymentInfo.NextExecutionTime,
		LastExecutionTime:       deploymentInfo.LastExecutionTime,
		Origin:                  deploymentInfo.Origin.String(),
		StartTime:               deploymentInfo.StartTime,
		IsRecurringDeployment:   isRecurring,
	}

	if !(deploymentInfo.DeploymentStage == model.Draft || deploymentInfo.DeploymentStage == model.Cancelled) {
		deploymentRest.TotalTaskCount = totalTaskCount
		deploymentRest.CompletedTaskCount = completedTaskCount
		deploymentRest.PendingTaskCount = deploymentRest.TotalTaskCount - completedTaskCount
		deploymentRest.SuccessTaskCount = successTaskCount
		deploymentRest.FailedTaskCount = failedTaskCount
	}

	return deploymentRest
}

func (service DeploymentService) performPartialUpdate(domainModel *model.Deployment, restModel rest.DeploymentRest) (map[string]map[string]interface{}, bool) {
	diffMap := PerformPartialUpdateForBaseRef(&domainModel.BaseEntityRefModel, restModel.BaseEntityRefModelRest)

	if restModel.PatchMap["description"] != nil && domainModel.Description != restModel.Description {
		common.PrepareInDiffMap("description", domainModel.Description, restModel.Description, &diffMap)
		domainModel.Description = restModel.Description
	}
	if restModel.PatchMap["displayName"] != nil && domainModel.DisplayName != restModel.DisplayName {
		common.PrepareInDiffMap("display_name", domainModel.DisplayName, restModel.DisplayName, &diffMap)
		domainModel.DisplayName = restModel.DisplayName
	}

	if restModel.PatchMap["deploymentType"] != nil {
		dpType, err := domainModel.DeploymentType.ToDeploymentType(restModel.DeploymentType)
		if err == nil && domainModel.DeploymentType != dpType {
			common.PrepareInDiffMap("deployment_type", domainModel.DeploymentType.String(), restModel.DeploymentType, &diffMap)
			domainModel.DeploymentType = dpType
		}
	}

	if restModel.PatchMap["deploymentStage"] != nil {
		dpStage := domainModel.DeploymentStage.ToDeploymentStage(restModel.DeploymentStage)
		if domainModel.DeploymentStage != dpStage {
			common.PrepareInDiffMap("deployment_stage", domainModel.DeploymentStage.String(), restModel.DeploymentStage, &diffMap)
			domainModel.DeploymentStage = dpStage
			if dpStage == model.InProgress {
				domainModel.StartTime = time.Now().UnixMilli()
			} else if dpStage == model.Completed {
				domainModel.LastExecutionTime = time.Now().UnixMilli()
			}
		}
	}

	if restModel.PatchMap["origin"] != nil {
		dpStage := domainModel.Origin.ToDeploymentOrigin(restModel.Origin)
		if domainModel.Origin != dpStage {
			common.PrepareInDiffMap("origin", domainModel.Origin.String(), restModel.Origin, &diffMap)
			domainModel.Origin = dpStage
		}
	}

	if restModel.PatchMap["refIds"] != nil && !reflect.DeepEqual(domainModel.RefIds, restModel.RefIds) {
		common.PrepareInDiffMap("ref_ids", domainModel.RefIds, restModel.RefIds, &diffMap)
		domainModel.RefIds = restModel.RefIds
	}

	if restModel.PatchMap["computerIds"] != nil && !reflect.DeepEqual(domainModel.ComputerIds, restModel.ComputerIds) {
		common.PrepareInDiffMap("computer_ids", domainModel.ComputerIds, restModel.ComputerIds, &diffMap)
		domainModel.ComputerIds = restModel.ComputerIds
	}

	if restModel.PatchMap["computerGroupIds"] != nil && !reflect.DeepEqual(domainModel.ComputerGroupIds, restModel.ComputerGroupIds) {
		common.PrepareInDiffMap("computer_group_ids", domainModel.ComputerGroupIds, restModel.ComputerGroupIds, &diffMap)
		domainModel.ComputerGroupIds = restModel.ComputerGroupIds
	}

	if restModel.PatchMap["assets"] != nil && !reflect.DeepEqual(domainModel.Assets, restModel.Assets) && domainModel.DeploymentStage == model.Draft {
		common.PrepareInDiffMap("assets", domainModel.Assets, restModel.Assets, &diffMap)
		domainModel.Assets = restModel.Assets
	}

	if restModel.PatchMap["platform_versions"] != nil && !reflect.DeepEqual(domainModel.PlatformVersions, restModel.PlatformVersions) && domainModel.DeploymentStage == model.Draft {
		common.PrepareInDiffMap("platform_versions", domainModel.PlatformVersions, restModel.PlatformVersions, &diffMap)
		domainModel.PlatformVersions = restModel.PlatformVersions
	}

	if restModel.PatchMap["scope"] != nil && !reflect.DeepEqual(domainModel.Scope, restModel.Scope) && domainModel.DeploymentStage == model.Draft {
		common.PrepareInDiffMap("scope", domainModel.Scope, restModel.Scope, &diffMap)
		domainModel.Scope = restModel.Scope
	}

	if restModel.PatchMap["deploymentPolicyId"] != nil && domainModel.DeploymentPolicyId != restModel.DeploymentPolicyId {
		common.PrepareInDiffMap("deployment_policy_id", domainModel.DeploymentPolicyId, restModel.DeploymentPolicyId, &diffMap)
		domainModel.DeploymentPolicyId = restModel.DeploymentPolicyId
	}

	if restModel.PatchMap["notifyEmailIds"] != nil && !reflect.DeepEqual(domainModel.NotifyEmailIds, restModel.NotifyEmailIds) {
		common.PrepareInDiffMap("notify_email_ids", domainModel.NotifyEmailIds, restModel.NotifyEmailIds, &diffMap)
		domainModel.NotifyEmailIds = restModel.NotifyEmailIds
	}

	if restModel.PatchMap["retryCount"] != nil && domainModel.RetryCount != restModel.RetryCount {
		common.PrepareInDiffMap("retry_count", domainModel.RetryCount, restModel.RetryCount, &diffMap)
		domainModel.RetryCount = restModel.RetryCount
	}

	if restModel.PatchMap["lastExecutionTime"] != nil && domainModel.LastExecutionTime != restModel.LastExecutionTime {
		common.PrepareInDiffMap("last_execution_time", domainModel.LastExecutionTime, restModel.LastExecutionTime, &diffMap)
		domainModel.LastExecutionTime = restModel.LastExecutionTime
	}

	if restModel.PatchMap["nextExecutionTime"] != nil && domainModel.NextExecutionTime != restModel.NextExecutionTime {
		common.PrepareInDiffMap("next_execution_time", domainModel.NextExecutionTime, restModel.NextExecutionTime, &diffMap)
		domainModel.NextExecutionTime = restModel.NextExecutionTime
	}

	return diffMap, len(diffMap) != 0
}

func (service DeploymentService) Create(rest rest.DeploymentRest) (int64, error) {
	rest.CreatedTime = common.CurrentMillisecond()
	rest.CreatedById = common.GetUserFromCallContext()
	deployment, err := service.convertToModel(rest)
	if err != nil {
		return 0, err
	}
	id, err := service.Repository.Create(deployment)
	if err != nil {
		return 0, err
	}
	go service.AfterCreate(deployment)
	return id, nil
}

func (service DeploymentService) Update(id int64, restModel rest.DeploymentRest) (bool, common.CustomError) {
	deployment, err := service.Repository.GetById(id, false)
	if err != nil {
		return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
	}

	//TODO Before update
	//customErr := service.BeforeUpdate(deployment, restModel)
	//if customErr.Message != "" {
	//	return false, customErr
	//}
	diffMap, isUpdatable := service.performPartialUpdate(&deployment, restModel)
	if isUpdatable {
		deployment.UpdatedTime = common.CurrentMillisecond()
		deployment.UpdatedById = common.GetUserFromCallContext()
		_, err := service.Repository.Update(&deployment)
		if err != nil {
			return false, common.CustomError{Message: err.Error(), Code: http.StatusInternalServerError}
		}
		go service.AfterUpdate(&deployment, diffMap)
		return true, common.CustomError{}
	} else {
		return isUpdatable, common.CustomError{}
	}
	//TODO: Handle after update event
}

func (service DeploymentService) GetDeployment(id int64, includeArchived bool) (rest.DeploymentRest, error) {
	var deploymentRest rest.DeploymentRest
	deployment, err := service.Repository.GetById(id, includeArchived)
	if err != nil {
		return deploymentRest, err
	}
	return service.convertToRest(deployment), nil
}

func (service DeploymentService) DeleteDeployment(id int64) (bool, error) {
	deployment, err := service.Repository.GetById(id, false)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error while get package to delete for id - %v, Error : %s ", id, err.Error()))
		return false, err
	}
	success, err := service.Repository.DeleteById(id)
	if err != nil {
		return success, err
	}
	go service.AfterDelete(deployment, false)
	return success, nil
}

func (service DeploymentService) AfterDelete(deployment model.Deployment, permanentDelete bool) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after delete process for deployment id : ", deployment.Id)
		}
	}()
	auditString := "Deployment "
	if permanentDelete {
		auditString = "is Deleted "
	} else {
		auditString = "is Archived "
	}
	auditString = fmt.Sprintf("-> Id : %v, Name : %s. ", deployment.Name, deployment.DisplayName)
	auditModel := common.DEPLOYMENT.String()
	audit := rest.DeleteAuditRest(auditString, auditModel, deployment.Id, common.CurrentMillisecond())
	_, err := NewAuditService().Create(audit)
	if err != nil {
		logger.ServiceLogger.Error("[DeploymentService][AfterDelete]", err)
	}

	NewAgentTaskService().Repository.DeleteByDeploymentId(deployment.Id)
	_, err = NewComplianceService().Repository.PermanentDeleteComplianceTaskResult(0, 0, deployment.Id, 0)
	if err != nil {
		logger.ServiceLogger.Error("[DeploymentService][AfterDelete]", err)
	}
}

func (service DeploymentService) GetAllDeployment(filter rest.SearchFilter) (rest.ListResponseRest, error) {
	var responsePage rest.ListResponseRest
	var deploymentPageList []model.Deployment
	var err error
	tableName := "deployments"
	queryCondition := ""
	qualifications := filter.Qualification
	if len(qualifications) > 1 {
		// need to handle this because search and ref model passed through qualifications
		var searchQualifications []rest.Qualification
		for _, qualification := range qualifications {
			if qualification.Column == "refModel" {
				queryCondition = "\"" + common.ToSnakeCase(qualification.Column) + "\" = '" + qualification.Value.(string) + "' "
			} else {
				searchQualifications = append(searchQualifications, qualification)
			}
		}
		filter.Qualification = searchQualifications
	}
	// Use secure parameterized queries to prevent SQL injection
	countQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, true, queryCondition)
	count := service.Repository.Count(countQueryResult.Query, countQueryResult.Parameters)
	if count > 0 {
		searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, queryCondition)
		deploymentPageList, err = service.Repository.GetAllDeployments(searchQueryResult.Query, searchQueryResult.Parameters)
		if err != nil {
			return responsePage, err
		}
	} else {
		responsePage.ObjectList = make([]interface{}, 0)
	}
	responsePage.ObjectList = service.ConvertListToRest(deploymentPageList)
	responsePage.TotalCount = count
	return responsePage, nil
}

func (service DeploymentService) GetDeployments(filter rest.SearchFilter) ([]model.Deployment, error) {
	var err error
	tableName := "deployments"
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, "")
	deploymentPageList, err := service.Repository.GetAllDeployments(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		return deploymentPageList, err
	}
	return deploymentPageList, nil
}

func (service DeploymentService) ConvertListToRest(deployments []model.Deployment) []rest.DeploymentRest {
	var deploymentRestList []rest.DeploymentRest
	if len(deployments) != 0 {
		for _, deployment := range deployments {
			deploymentRest := service.convertToRest(deployment)
			deploymentRestList = append(deploymentRestList, deploymentRest)
		}
	}
	return deploymentRestList
}

func (service DeploymentService) AfterCreate(deployment *model.Deployment) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after create process for deployment id : ", deployment.Id, err)
		}
	}()

	deployment.Name = fmt.Sprintf("ADR-%03d", deployment.Id)

	computerIds, err := NewAgentService().GetAllAssetIdsByScope(deployment.AgentScopeFilter)
	if err == nil {
		deployment.ComputerIds = computerIds
	}
	_, err = service.Repository.Update(deployment)
	if err != nil {
		logger.ServiceLogger.Error("[DeploymentService][AfterCreate]", err)
	}
	hasMultipleExecution := false
	if deployment.DeploymentStage == model.Initiated {
		policy, err := NewDeploymentPolicyService().Repository.GetById(deployment.DeploymentPolicyId, false)
		if err == nil && policy.Type == model.Schedule && policy.InitiateDeploymentOn == model.Recurring {
			hasMultipleExecution = true
		}
		agentTaskService := NewAgentTaskService()
		uniquePkgIds := make(map[int64]int64)
		if deployment.IsPkgSelectAsBundle {
			bundleService := NewDeploymentBundleService()
			for _, refId := range deployment.RefIds {
				bundle, err := bundleService.GetById(refId, false)
				if err == nil {
					for _, id := range bundle.ReferenceIds {
						uniquePkgIds[id] = refId
					}
				}
			}
		} else {
			for _, id := range deployment.RefIds {
				uniquePkgIds[id] = id
			}
		}

		if strings.EqualFold("patch", deployment.RefModel) && len(uniquePkgIds) > 0 {
			var compIds []int64
			if deployment.ComputerIds != nil && len(deployment.ComputerIds) > 0 {
				for _, compId := range deployment.ComputerIds {
					if deployment.DeploymentType == model.Install {
						searchFilter := rest.SearchFilter{
							Qualification: []rest.Qualification{
								rest.BuildQualification("patch_id", "in", uniquePkgIds, "and"),
								rest.BuildQualification("assetId", "equals", compId, "and"),
								rest.BuildQualification("patch_state", "equals", fmt.Sprintf("%d", model.Missing), "and"),
								rest.BuildQualification("is_old", "equals", false, "and"),
							},
						}
						missingCountQuery := rest.PrepareSecureQueryFromSearchFilter(searchFilter, common.ASSET_PATCH_RELATION.String(), true, "")
						missingEndPoints := service.Repository.Count(missingCountQuery.Query, missingCountQuery.Parameters)
						if missingEndPoints > 0 {
							compIds = append(compIds, compId)
						}
					} else if deployment.DeploymentType == model.Uninstall {
						searchFilter := rest.SearchFilter{
							Qualification: []rest.Qualification{
								rest.BuildQualification("patch_id", "in", uniquePkgIds, "and"),
								rest.BuildQualification("assetId", "equals", compId, "and"),
								rest.BuildQualification("patch_state", "equals", fmt.Sprintf("%d", model.Installed), "and"),
								rest.BuildQualification("is_old", "equals", false, "and"),
							},
						}
						installCountQuery := rest.PrepareSecureQueryFromSearchFilter(searchFilter, common.ASSET_PATCH_RELATION.String(), true, "")
						installEndPoints := service.Repository.Count(installCountQuery.Query, installCountQuery.Parameters)
						if installEndPoints > 0 {
							compIds = append(compIds, compId)
						}
					}
				}
			}

			deployment.ComputerIds = compIds
		}

		if uniquePkgIds != nil && len(uniquePkgIds) > 0 {
			for pkgId, bundleId := range uniquePkgIds {
				for _, computerId := range deployment.ComputerIds {
					if deployment.RefModel == common.COMPLIANCES.String() {
						agentTask := rest.AgentTaskRest{
							BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
								RefId:    pkgId,
								RefModel: deployment.RefModel,
							},
							TaskType:             model.DEPLOYMENT.String(),
							AgentId:              computerId,
							DeploymentId:         deployment.Id,
							TaskStatus:           model.TaskReadyToDeploy.String(),
							HasMultipleExecution: hasMultipleExecution,
							CustomTaskDetails:    map[string]interface{}{"bundleId": bundleId},
						}
						_, err := agentTaskService.Create(agentTask)
						if err != nil {
							logger.ServiceLogger.Error("[DeploymentService][AfterCreate]", err)
						}

					} else if (deployment.RefModel == common.PATCH.String() || deployment.RefModel == "Patch") && deployment.DeploymentType == model.Uninstall {
						agentTask := rest.AgentTaskRest{
							BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
								RefId:    pkgId,
								RefModel: deployment.RefModel,
							},
							TaskType:             model.DEPLOYMENT.String(),
							AgentId:              computerId,
							DeploymentId:         deployment.Id,
							TaskStatus:           model.TaskReadyToDeploy.String(),
							HasMultipleExecution: hasMultipleExecution,
						}
						_, err := agentTaskService.Create(agentTask)
						if err != nil {
							logger.ServiceLogger.Error("[DeploymentService][AfterCreate]", err)
						}
					} else {
						agentTask := rest.AgentTaskRest{
							BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
								RefId:    pkgId,
								RefModel: deployment.RefModel,
							},
							TaskType:             model.DEPLOYMENT.String(),
							AgentId:              computerId,
							DeploymentId:         deployment.Id,
							TaskStatus:           model.TaskWaiting.String(),
							HasMultipleExecution: hasMultipleExecution,
						}
						taskId, err := agentTaskService.Create(agentTask)
						if err == nil {
							service.DownloadPatchPackageFileForDeployment(taskId, pkgId, computerId, deployment.RefModel)
						}
					}

				}
			}
		} else {
			deployment.DeploymentStage = model.Completed
			_, err = service.Repository.Update(deployment)
		}
	}

	go service.CreateAudit(deployment)
}

func (service DeploymentService) DownloadPatchPackageFileForDeployment(taskId, pkgId, compId int64, refModel string) {
	if refModel == common.PATCH.String() || refModel == "Patch" {
		taskStatus := service.DownloadPatch(pkgId, compId)
		agentTaskService := NewAgentTaskService()
		taskRest, err := agentTaskService.GetAgentTaskById(taskId)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}

		taskRest.TaskStatus = taskStatus
		taskRest.PatchMap = map[string]interface{}{"taskStatus": taskStatus}
		agentTaskService.Update(taskId, taskRest)
	} else if refModel == common.PACKAGE.String() || refModel == "package" {
		fileServerConfig := model.FileServerConfig{}
		assetRest, err := NewAgentService().GetAssetMetaDataById(compId)
		if err == nil {
			fileServerConfig, _ = NewFileServerConfigService().Repository.GetByLocation(assetRest.Location, false)
		}
		taskStatus := model.TaskReadyToDeploy.String()

		if fileServerConfig.Id > 0 {
			taskStatus = service.DownloadPackageFileFromFileServer(pkgId, fileServerConfig)
		}
		agentTaskService := NewAgentTaskService()
		taskRest, err := agentTaskService.GetAgentTaskById(taskId)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}

		taskRest.TaskStatus = taskStatus
		taskRest.PatchMap = map[string]interface{}{"taskStatus": taskStatus}
		agentTaskService.Update(taskId, taskRest)
	} else {
		agentTaskService := NewAgentTaskService()
		taskRest, err := agentTaskService.GetAgentTaskById(taskId)
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
		taskRest.TaskStatus = model.TaskReadyToDeploy.String()
		taskRest.PatchMap = map[string]interface{}{"taskStatus": model.TaskReadyToDeploy.String()}
		agentTaskService.Update(taskId, taskRest)
	}
}

func (service DeploymentService) DownloadPatch(pkgId, compId int64) string {
	patchService := NewPatchService()
	patch, err := patchService.Repository.GetById(pkgId, false)
	patchRest := patchService.convertToPatchRest(patch)
	if err == nil {
		if patch.PatchUpdateCategory == model.UPGRADES {
			if len(patch.DownloadFileDetails) > 0 {
				for _, fileDetails := range patch.DownloadFileDetails {
					if strings.HasSuffix(fileDetails.FileName, ".iso") {
						return model.TaskReadyToDeploy.String()
					}
				}
			}
		} else {
			if patchRest.DownloadStatus == model.SUCCESS.String() {
				return model.TaskReadyToDeploy.String()
			}

			// if download status is in progress and someone deploying that patch that task will be cancelled
			// download status == IN_PROGRESS than task status == cancelled
			if patchRest.DownloadStatus == model.None.String() || patchRest.DownloadStatus == model.FAILED.String() || patchRest.DownloadStatus == model.PENDING.String() {
				patch.DownloadStatus = model.IN_PROGRESS
				_, err = patchService.Repository.Update(&patch)
				if err != nil {
					logger.ServiceLogger.Error("[DeploymentService][DownloadPatch]", err)
				}
				var newFileDetails []model.PatchFileData
				assetRest, err := NewAgentService().GetAssetMetaDataById(compId)
				if err == nil {
					fileServerConfig, err := NewFileServerConfigService().Repository.GetByLocation(assetRest.Location, false)
					if err == nil {
						return service.DownloadPatchFileFromFileServer(pkgId, patchRest, fileServerConfig)
					} else {
						return DownloadFileInMainServer(patchRest, newFileDetails, pkgId)
					}
				} else {
					return DownloadFileInMainServer(patchRest, newFileDetails, pkgId)
				}
			}
		}
	}
	return model.TaskCancelled.String()
}

func DownloadFileInMainServer(patchRest rest.PatchRest, newFileDetails []model.PatchFileData, pkgId int64) string {
	existingFileMap := map[string]interface{}{}
	fileDataService := NewFileDataService()
	certFile := ""
	keyFile := ""
	if patchRest.OsPlatform == common.Redhat.String() {
		certFile, keyFile = redhat.NewRedhatPatchService().RetrieveCertificateFilePath(patchRest.UUID)
	}
	for _, fileDetail := range patchRest.DownloadFileDetails {
		if _, ok := existingFileMap[fileDetail.FileName]; !ok && (!strings.Contains(strings.ToLower(fileDetail.FileName), ".psf") && !strings.Contains(strings.ToLower(fileDetail.FileName), ".wim") && (strings.Contains(strings.ToLower(fileDetail.FileName), ".exe") || strings.Contains(strings.ToLower(fileDetail.FileName), ".msi") || strings.Contains(strings.ToLower(fileDetail.FileName), ".msu") || strings.Contains(strings.ToLower(fileDetail.FileName), ".cab") || strings.Contains(strings.ToLower(fileDetail.FileName), ".dmg") || strings.Contains(strings.ToLower(fileDetail.FileName), ".pkg") ||
			strings.Contains(strings.ToLower(fileDetail.FileName), ".deb") || strings.Contains(strings.ToLower(fileDetail.FileName), ".rpm"))) {
			existingFileMap[fileDetail.FileName] = true
			details := model.PatchFileData{}
			details.FileName = fileDetail.FileName
			details.ReleaseDate = fileDetail.ReleaseDate
			details.ChecksumSHA256 = fileDetail.ChecksumSHA256
			details.DownloadUrl = fileDetail.DownloadUrl
			details.RefName = ""
			if fileDetail.RefName == "" {
				fileRefName := uuid.New().String()
				if patchRest.OsPlatform == common.Redhat.String() {
					if certFile != "" && keyFile != "" {
						fileDataRest, customErr := fileDataService.DownloadFileFromURLByCert(fileDetail.DownloadUrl, fileRefName, "rpm", common.PatchFilePath(patchRest.Name), certFile, keyFile)
						if customErr.Message == "" {
							details.RefName = fileDataRest.RefName
							fileDataRest.Name = patchRest.Name
							_, err := fileDataService.CreateFileData(fileDataRest)
							if err != nil {
								logger.ServiceLogger.Error("[DeploymentService][DownloadPatch]", err)
							}
						}
						details.Url = fileDetail.Url
						details.Size = fileDetail.Size
						details.Language = fileDetail.Language
						details.FetchFromMSU = fileDetail.FetchFromMSU
						if customErr.Message == "" {
							newFileDetails = append(newFileDetails, details)
						}
					}
				} else {
					fileDataRest, customErr := fileDataService.DownloadFileFromURL(fileDetail.DownloadUrl, fileRefName, "exe", common.PatchFilePath(patchRest.Name))
					if customErr.Message == "" {
						details.RefName = fileDataRest.RefName
						fileDataRest.Name = patchRest.Name
						_, err := fileDataService.CreateFileData(fileDataRest)
						if err != nil {
							logger.ServiceLogger.Error("[DeploymentService][DownloadPatch]", err)
						}
					}
					details.Url = fileDetail.Url
					details.Size = fileDetail.Size
					details.Language = fileDetail.Language
					details.FetchFromMSU = fileDetail.FetchFromMSU
					if customErr.Message == "" {
						newFileDetails = append(newFileDetails, details)
					}
				}
			}
		}
	}
	if len(newFileDetails) > 0 {
		patchRest.DownloadFileDetails = newFileDetails
		patchRest.DownloadStatus = model.SUCCESS.String()
		patchRest.PatchMap = map[string]interface{}{"downloadFileDetails": newFileDetails, "downloadStatus": model.SUCCESS.String()}
		NewPatchService().Update(pkgId, patchRest)
		return model.TaskReadyToDeploy.String()
	} else {
		patchRest.DownloadFileDetails = newFileDetails
		patchRest.DownloadStatus = model.FAILED.String()
		patchRest.PatchMap = map[string]interface{}{"downloadStatus": model.FAILED.String(), "downloadError": "failed to download file from downloadUrl"}
		NewPatchService().Update(pkgId, patchRest)
	}

	return model.Cancelled.String()
}

func (service DeploymentService) DownloadPatchFileFromFileServer(pkgId int64, patchRest rest.PatchRest, fileServerConfig model.FileServerConfig) string {
	patchService := NewPatchService()
	var pchFileDetails model.PatchFileDownloadStatus
	var payload []byte
	url := fmt.Sprintf("%s/api/file-server/request-download-patch/%s", fileServerConfig.Url, patchRest.Name)
	payload, _ = json.Marshal(patchRest.DownloadFileDetails)
	response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.FileServerAuthToken()})
	if success && response != nil && len(response) > 0 {
		err := json.Unmarshal(response, &pchFileDetails)
		if err == nil {
			if pchFileDetails.IsDownloadFailed {
				patchRest.DownloadStatus = model.FAILED.String()
				patchRest.DownloadError = pchFileDetails.Error
				patchRest.PatchMap = map[string]interface{}{"downloadStatus": model.FAILED.String(), "downloadError": patchRest.DownloadError}
				patchService.Update(pkgId, patchRest)

				return model.TaskCancelled.String()
			} else {
				var newPchFileDetails []model.PatchFileData
				for _, detail := range pchFileDetails.PatchFileData {
					detail.LocationId = fileServerConfig.LocationId
					detail.Url = detail.DownloadUrl
					detail.DownloadUrl = fileServerConfig.Url + "/api/file-server/download/" + patchRest.Name + "/" + detail.RefName
					detail.RefName = ""
					newPchFileDetails = append(newPchFileDetails, detail)
				}
				patchRest.DownloadFileDetails = newPchFileDetails
				patchRest.DownloadStatus = model.SUCCESS.String()
				patchRest.PatchMap = map[string]interface{}{"downloadFileDetails": newPchFileDetails, "downloadStatus": model.SUCCESS.String()}
				patchService.Update(pkgId, patchRest)

				return model.TaskReadyToDeploy.String()
			}
		}
	} else {
		patchRest.DownloadStatus = model.FAILED.String()
		patchRest.DownloadError = pchFileDetails.Error
		patchRest.PatchMap = map[string]interface{}{"downloadStatus": model.FAILED.String(), "downloadError": "failed to connect with file server"}
		patchService.Update(pkgId, patchRest)
	}
	return model.Cancelled.String()
}

func (service DeploymentService) DownloadPackageFileFromFileServer(pkgId int64, fileServerConfig model.FileServerConfig) string {
	packageService := NewPackageService()
	packageRest, err := packageService.GetPackage(pkgId, false)
	if err == nil {
		var pchFileDetails model.PatchFileDownloadStatus
		var payload []byte
		url := fmt.Sprintf("%s/api/file-server/request-download-package/%s", fileServerConfig.Url, packageRest.Name)
		payload, _ = json.Marshal(packageRest.PkgFilePath)
		response, success := common.ExecutePostRequest(url, payload, map[string]string{"Authorization": common.FileServerAuthToken()})
		if success && response != nil && len(response) > 0 {
			err := json.Unmarshal(response, &pchFileDetails)
			if err == nil {
				if !pchFileDetails.IsDownloadFailed {
					var newPchFileDetails []model.FileMetaData
					for _, detail := range pchFileDetails.PkgFileData {
						detail.LocationId = fileServerConfig.LocationId
						detail.Url = fileServerConfig.Url + "/api/download/" + packageRest.Name + "/" + detail.RefName
						newPchFileDetails = append(newPchFileDetails, detail)
					}
					var pkgFileDataRest []rest.FileMetaDataRest
					for _, dataRest := range newPchFileDetails {
						pkgFileDataRest = append(pkgFileDataRest, rest.ConvertToFileMetaDataRest(dataRest))
					}
					packageRest.PkgFilePathList = pkgFileDataRest
					packageRest.PatchMap = map[string]interface{}{"pkgFilePathList": pkgFileDataRest}
					packageService.Update(pkgId, packageRest)

					return model.TaskReadyToDeploy.String()
				}
			}
		}
	}

	return model.TaskCancelled.String()
}

func (service DeploymentService) CreateAudit(deployment *model.Deployment) {
	auditString := fmt.Sprintf("Deployment is created with Id : %v, Name : %s, Deployment Stage : %v.",
		deployment.Name, deployment.DisplayName, deployment.DeploymentStage.String())
	auditModel := common.DEPLOYMENT.String()
	audit := rest.CreateAuditRest(auditString, auditModel, deployment.Id, common.CurrentMillisecond())
	_, err := NewAuditService().Create(audit)
	if err != nil {
		logger.ServiceLogger.Error("[DeploymentService][CreateAudit]", err)
	}
}

func (service DeploymentService) AfterUpdate(deployment *model.Deployment, diffMap map[string]map[string]interface{}) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error("Error while performing after update process for deployment id : ", deployment.Id, err)
		}
	}()
	if diffMap != nil && len(diffMap) > 0 {
		hasMultipleExecution := false
		policy, err := NewDeploymentPolicyService().Repository.GetById(deployment.DeploymentPolicyId, false)
		if err == nil && policy.Type == model.Schedule && policy.InitiateDeploymentOn == model.Recurring {
			hasMultipleExecution = true
		}
		if diffMap["scope"] != nil || diffMap["asset_id"] != nil || deployment.DeploymentStage == model.Draft || deployment.DeploymentStage == model.Initiated {
			computerIds, err := NewAgentService().GetAllAssetIdsByScope(deployment.AgentScopeFilter)
			if err == nil {
				if strings.EqualFold("patch", deployment.RefModel) {
					var compIds []int64
					for _, compId := range deployment.ComputerIds {
						uniquePkgIds := make(map[int64]int64)
						if deployment.IsPkgSelectAsBundle {
							bundleService := NewDeploymentBundleService()
							for _, refId := range deployment.RefIds {
								bundle, err := bundleService.GetById(refId, false)
								if err == nil {
									for _, id := range bundle.ReferenceIds {
										uniquePkgIds[id] = id
									}
								}
							}
						} else {
							for _, id := range deployment.RefIds {
								uniquePkgIds[id] = id
							}
						}
						if len(uniquePkgIds) > 0 {
							if deployment.DeploymentType == model.Install {
								seachFilter := rest.SearchFilter{
									Qualification: []rest.Qualification{
										rest.BuildQualification("patch_id", "in", uniquePkgIds, "and"),
										rest.BuildQualification("assetId", "equals", compId, "and"),
										rest.BuildQualification("patch_state", "equals", fmt.Sprintf("%d", model.Missing), "and"),
										rest.BuildQualification("is_old", "equals", false, "and"),
									},
								}
								missingCountQuery := rest.PrepareSecureQueryFromSearchFilter(seachFilter, common.ASSET_PATCH_RELATION.String(), true, "")
								missingEndPoints := service.Repository.Count(missingCountQuery.Query, missingCountQuery.Parameters)
								if missingEndPoints > 0 {
									compIds = append(compIds, compId)
								}
							} else if deployment.DeploymentType == model.Uninstall {
								searchFilter := rest.SearchFilter{
									Qualification: []rest.Qualification{
										rest.BuildQualification("patch_id", "in", uniquePkgIds, "and"),
										rest.BuildQualification("assetId", "equals", compId, "and"),
										rest.BuildQualification("patch_state", "equals", fmt.Sprintf("%d", model.Installed), "and"),
										rest.BuildQualification("is_old", "equals", false, "and"),
									},
								}
								installedCountQuery := rest.PrepareSecureQueryFromSearchFilter(searchFilter, common.ASSET_PATCH_RELATION.String(), true, "")
								installedEndPoints := service.Repository.Count(installedCountQuery.Query, installedCountQuery.Parameters)
								if installedEndPoints > 0 {
									compIds = append(compIds, compId)
								}
							}

						}
					}
					deployment.ComputerIds = compIds
				} else {
					deployment.ComputerIds = computerIds
				}
			}
			_, err = service.Repository.Update(deployment)
			if err != nil {
				logger.ServiceLogger.Error("[DeploymentService][AfterUpdate]", err)
			}
		}

		if diffMap["deployment_stage"] != nil {
			if deployment.DeploymentStage == model.Initiated {
				deploymentStage := diffMap["deployment_stage"]
				newDeploymentStage := deploymentStage["newvalue"]
				if newDeploymentStage == model.Initiated.String() {
					agentTaskService := NewAgentTaskService()
					uniquePkgIds := make(map[int64]int64)
					if deployment.IsPkgSelectAsBundle {
						bundleService := NewDeploymentBundleService()
						for _, refId := range deployment.RefIds {
							bundle, err := bundleService.GetById(refId, false)
							if err == nil {
								for _, id := range bundle.ReferenceIds {
									uniquePkgIds[id] = refId
								}
							}
						}
					} else {
						for _, id := range deployment.RefIds {
							uniquePkgIds[id] = id
						}
					}

					if strings.EqualFold("patch", deployment.RefModel) {
						var compIds []int64
						for _, compId := range deployment.ComputerIds {
							missingCountQuery := rest.PrepareSecureQueryFromSearchFilter(rest.SearchFilter{
								Qualification: []rest.Qualification{
									rest.BuildQualification("patch_id", "in", uniquePkgIds, "and"),
									rest.BuildQualification("assetId", "equals", compId, "and"),
									rest.BuildQualification("patch_state", "equals", fmt.Sprintf("%d", model.Missing), "and"),
									rest.BuildQualification("is_old", "equals", false, "and"),
								},
							}, common.ASSET_PATCH_RELATION.String(), true, "")
							missingEndPoints := service.Repository.Count(missingCountQuery.Query, missingCountQuery.Parameters)
							if missingEndPoints > 0 {
								compIds = append(compIds, compId)
							}
						}
						deployment.ComputerIds = compIds
						_, err = service.Repository.Update(deployment)
						if err != nil {
							logger.ServiceLogger.Error("[DeploymentService][AfterUpdate]", err)
						}
					}

					if uniquePkgIds != nil && len(uniquePkgIds) > 0 {
						for pkgId, bundleId := range uniquePkgIds {
							for _, computerId := range deployment.ComputerIds {
								if deployment.RefModel == common.COMPLIANCES.String() {
									agentTask := rest.AgentTaskRest{
										BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
											RefId:    pkgId,
											RefModel: deployment.RefModel,
										},
										TaskType:             model.DEPLOYMENT.String(),
										AgentId:              computerId,
										DeploymentId:         deployment.Id,
										TaskStatus:           model.TaskReadyToDeploy.String(),
										HasMultipleExecution: hasMultipleExecution,
										CustomTaskDetails:    map[string]interface{}{"bundleId": bundleId},
									}
									_, err := agentTaskService.Create(agentTask)
									if err != nil {
										logger.ServiceLogger.Error("[DeploymentService][AfterUpdate]", err)
									}

								} else if (deployment.RefModel == common.PATCH.String() || deployment.RefModel == "Patch") && deployment.DeploymentType == model.Uninstall {
									agentTask := rest.AgentTaskRest{
										BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
											RefId:    pkgId,
											RefModel: deployment.RefModel,
										},
										TaskType:             model.DEPLOYMENT.String(),
										AgentId:              computerId,
										DeploymentId:         deployment.Id,
										TaskStatus:           model.TaskReadyToDeploy.String(),
										HasMultipleExecution: hasMultipleExecution,
									}
									_, err := agentTaskService.Create(agentTask)
									if err != nil {
										logger.ServiceLogger.Error("[DeploymentService][AfterUpdate]", err)
									}
								} else {
									agentTask := rest.AgentTaskRest{
										BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
											RefId:    pkgId,
											RefModel: deployment.RefModel,
										},
										TaskType:             model.DEPLOYMENT.String(),
										AgentId:              computerId,
										DeploymentId:         deployment.Id,
										TaskStatus:           model.TaskWaiting.String(),
										HasMultipleExecution: hasMultipleExecution,
									}
									taskId, err := agentTaskService.Create(agentTask)
									if err == nil {
										service.DownloadPatchPackageFileForDeployment(taskId, pkgId, computerId, deployment.RefModel)
									}
								}

							}
						}
					} else {
						deployment.DeploymentStage = model.Completed
						_, err = service.Repository.Update(deployment)
					}
				}
			} else if deployment.DeploymentStage == model.Pause || deployment.DeploymentStage == model.Resume {
				for _, computerId := range deployment.ComputerIds {
					agentTask := rest.AgentTaskRest{
						TaskType:     model.HOLD_DEPLOYMENT_EXECUTION.String(),
						AgentId:      computerId,
						DeploymentId: deployment.Id,
						TaskStatus:   model.TaskReadyToDeploy.String(),
					}
					_, err = NewAgentTaskService().Create(agentTask)
					if err != nil {
						logger.ServiceLogger.Error("[DeploymentService][AfterUpdate]", err)
					}
				}
			}
		}
	}

	go service.UpdateAudit(deployment, diffMap)
}

func (service DeploymentService) PrepareDeploymentCompletedReport(deploymentId int64) {
	defer func() {
		if err := recover(); err != nil {
			logger.ServiceLogger.Error(err)
		}
	}()
	deployment, err := NewDeploymentService().GetDeployment(deploymentId, false)
	if err == nil && deployment.NotifyEmailIds != nil && len(deployment.NotifyEmailIds) > 0 {
		htmlBodyContent := DefaultReportFormat()
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$report_title}}", "Deployment Completed Report")
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$report_body}}", DeploymentCompleteReportFormat())
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_name}}", deployment.Name)
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_display_name}}", deployment.DisplayName)
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_type}}", strings.ToUpper(deployment.DeploymentType))
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_stage}}", strings.ToUpper(deployment.DeploymentStage))
		userDetails, exist := cache.UserCache.Get(deployment.CreatedById)
		if exist {
			if userDetail, ok := userDetails.(map[string]interface{}); ok {
				if len(userDetail) != 0 {
					if username, ok := userDetail["full_name"]; ok {
						htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_created_by}}", username.(string))
					}
				}
			}
		}
		if deployment.CreatedTime != 0 {
			t := time.Unix(deployment.CreatedTime/1000, 0)
			formattedTime := t.Format("2006/01/02 03:04:05 PM")
			htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_created_on}}", formattedTime)
		}
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_policy_name}}", NewDeploymentPolicyService().Repository.GetDisplayNameById(deployment.DeploymentPolicyId))
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_description}}", fmt.Sprint(deployment.Description))
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_total_task}}", fmt.Sprint(deployment.TotalTaskCount))
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_success_task}}", fmt.Sprint(deployment.SuccessTaskCount))
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$deployment_failed_task}}", fmt.Sprint(deployment.FailedTaskCount))
		htmlBodyContent = strings.ReplaceAll(htmlBodyContent, "{{$server_url}}", common.MainServerUrl())
		logger.ServiceLogger.Info("Mail body:", htmlBodyContent)

		EmailConfig{}.SendMail(deployment.NotifyEmailIds, fmt.Sprintf("(%s : %s) Deployment Completed", deployment.Name, deployment.DisplayName), htmlBodyContent)
	}
}

func (service DeploymentService) UpdateAudit(deployment *model.Deployment, diffMap map[string]map[string]interface{}) {
	auditString := fmt.Sprintf("Deployment - %v is Updated. -> ", deployment.Name)
	auditString = service.generateUpdateAuditString(auditString, diffMap)
	auditModel := common.DEPLOYMENT.String()
	audit := rest.UpdateAuditRest(auditString, auditModel, deployment.Id, common.CurrentMillisecond())
	_, err := NewAuditService().Create(audit)
	if err != nil {
		logger.ServiceLogger.Error("[DeploymentService][PrepareDeploymentCompletedReport]", err)
	}
}

func (service DeploymentService) generateUpdateAuditString(auditString string, diffMap map[string]map[string]interface{}) string {
	if len(diffMap) > 0 {
		for key, innerMap := range diffMap {
			switch key {
			case "display_name":
				auditString += common.UpdateAuditStringWithFromTo("Display Name", innerMap)
				break
			case "description":
				auditString += common.UpdateAuditStringWithFromTo("Description", innerMap)
				break
			case "deployment_policy_id":
				oldId := innerMap["oldvalue"].(int64)
				newId := innerMap["newvalue"].(int64)
				oldName := NewDeploymentPolicyService().Repository.GetNameById(oldId)
				newName := NewDeploymentPolicyService().Repository.GetNameById(newId)
				auditString += fmt.Sprintf("Deployment Policy : %v to %v, ", oldName, newName)
				break
			case "deployment_type":
				auditString += common.UpdateAuditStringWithFromTo("Deployment Type", innerMap)
				break
			case "deployment_stage":
				auditString += common.UpdateAuditStringWithFromTo("Deployment Stage", innerMap)
				break
			case "scope":
				oldId := innerMap["oldvalue"].(int64)
				newId := innerMap["newvalue"].(int64)
				oldName := model.Int64ToAgentScope(oldId)
				newName := model.Int64ToAgentScope(newId)
				auditString += fmt.Sprintf("Scope : %v to %v, ", oldName, newName)
				break
			case "notify_email_ids":
				auditString += "Notify Emails is updated, "
				break
			case "retry_count":
				auditString += common.UpdateAuditStringWithFromTo("Retry Count", innerMap)
				break
			}
		}
	}
	auditString = auditString[:len(auditString)-2] + "."
	return auditString
}

func (service DeploymentService) GetDeploymentHeatMap(id int64, filter rest.SearchFilter) (map[string]interface{}, error) {
	from, to := rest.ExtractTimelineToMillisecond(filter.Timeline)
	heatMap := map[string]interface{}{}
	deployment, err := service.Repository.GetById(id, false)
	if err != nil || deployment.Id == 0 || common.COMPLIANCES.String() != deployment.RefModel || !deployment.IsPkgSelectAsBundle {
		return heatMap, err
	}
	assetIdList := common.GetAccessibleAssetIds()
	bundleService := NewDeploymentBundleService()
	complianceService := NewComplianceService()
	if deployment.RefIds != nil && len(deployment.RefIds) > 0 {
		ids := make([]int64, len(deployment.RefIds))
		for i, id := range deployment.RefIds {
			ids[i] = id
		}
		filter = rest.SearchFilter{
			Qualification: []rest.Qualification{
				rest.BuildQualification("id", "in", ids, "AND"),
			},
		}
		var bundlesList []model.DeploymentBundle
		searchQuery := rest.PrepareSecureQueryFromSearchFilter(filter, common.DEPLOYMENT_BUNDLE.String(), false, "")
		bundlesList, err = bundleService.Repository.GetAllBundles(searchQuery.Query, searchQuery.Parameters)
		if err != nil {
			return heatMap, err
		}
		for _, bundle := range bundlesList {
			taskState := map[string]interface{}{}
			ids = make([]int64, len(bundle.ReferenceIds))
			for i, id := range bundle.ReferenceIds {
				ids[i] = id
			}
			complianceTaskResults, _ := complianceService.Repository.GetDeploymentComplianceTaskResults(assetIdList, ids, deployment.Id, bundle.Id, from, to)
			if complianceTaskResults != nil {
				filter = rest.SearchFilter{
					Qualification: []rest.Qualification{
						rest.BuildQualification("id", "in", ids, "AND"),
					},
				}
				// Use secure parameterized queries to prevent SQL injection
				searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, common.COMPLIANCES.String(), false, "")
				complianceMap := map[string]interface{}{}
				complianceList, _ := complianceService.Repository.GetAllCompliance(searchQueryResult.Query, searchQueryResult.Parameters)
				for _, compliance := range complianceList {
					complianceMap[common.ConvertIntToString(compliance.Id)] = compliance
				}
				for _, complianceTaskResult := range complianceTaskResults {
					taskStatusState := map[string]interface{}{}
					taskStatusState["total"] = complianceTaskResult["total_count"]
					taskStatusState[model.TaskSuccess.String()] = complianceTaskResult["success_count"]
					taskStatusState[model.TaskFailed.String()] = complianceTaskResult["failed_count"]
					compliance, _ := complianceMap[common.ConvertIntToString(complianceTaskResult["compliance_id"])].(model.Compliance)
					taskStatusState["metadata"] = map[string]interface{}{
						"name":        compliance.DisplayName,
						"bindings":    compliance.Bindings,
						"description": compliance.Description,
						"ruleType":    compliance.RuleType.String(),
						"impact":      compliance.Impact.String(),
					}
					taskState[common.ConvertIntToString(complianceTaskResult["compliance_id"])] = taskStatusState
				}
			}
			taskState["metadata"] = map[string]interface{}{
				"name":        bundle.Name,
				"iconFile":    bundle.IconFile,
				"description": bundle.Description,
			}
			heatMap[common.ConvertIntToString(bundle.Id)] = taskState
		}
	}
	return heatMap, nil
}

func (service DeploymentService) PostOperationAddAsset(assetId int64) {
	var filter rest.SearchFilter
	var deploymentPageList []model.Deployment
	var err error
	tableName := "deployments"
	queryCondition := ""
	var refModels []string
	refModels = append(refModels, common.COMPLIANCES.String())
	refModels = append(refModels, common.CONFIGURATION.String())
	filter = rest.SearchFilter{
		Qualification: []rest.Qualification{
			rest.BuildQualification("ref_model", "in", refModels, "and"),
		},
	}
	// Use secure parameterized queries to prevent SQL injection
	searchQueryResult := rest.PrepareSecureQueryFromSearchFilter(filter, tableName, false, queryCondition)
	deploymentPageList, err = service.Repository.GetAllDeployments(searchQueryResult.Query, searchQueryResult.Parameters)
	if err != nil {
		logger.ServiceLogger.Error(err)
	}
	if len(deploymentPageList) > 0 {
		for _, deployment := range deploymentPageList {
			hasMultipleExecution := false
			policy, err := NewDeploymentPolicyService().Repository.GetById(deployment.DeploymentPolicyId, false)
			if err == nil && policy.Type == model.Schedule && policy.InitiateDeploymentOn == model.Recurring {
				hasMultipleExecution = true
			}
			if hasMultipleExecution {
				computerIds, _ := NewAgentService().GetAllAssetIdsByScope(deployment.AgentScopeFilter)
				if computerIds != nil && len(computerIds) > 0 {
					validAsset := false
					for _, v := range computerIds {
						if v == assetId {
							validAsset = true
						}
					}
					if validAsset {
						if deployment.DeploymentStage != model.Draft && deployment.DeploymentStage != model.Initiated {
							agentTaskService := NewAgentTaskService()
							uniquePkgIds := make(map[int64]int64)
							if deployment.IsPkgSelectAsBundle {
								bundleService := NewDeploymentBundleService()
								for _, refId := range deployment.RefIds {
									bundle, err := bundleService.GetById(refId, false)
									if err == nil {
										for _, id := range bundle.ReferenceIds {
											uniquePkgIds[id] = refId
										}
									}
								}
							} else {
								for _, id := range deployment.RefIds {
									uniquePkgIds[id] = id
								}
							}

							if uniquePkgIds != nil && len(uniquePkgIds) > 0 {
								for pkgId, bundleId := range uniquePkgIds {
									if deployment.RefModel == common.COMPLIANCES.String() {
										agentTask := rest.AgentTaskRest{
											BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
												RefId:    pkgId,
												RefModel: deployment.RefModel,
											},
											TaskType:             model.DEPLOYMENT.String(),
											AgentId:              assetId,
											DeploymentId:         deployment.Id,
											TaskStatus:           model.TaskReadyToDeploy.String(),
											HasMultipleExecution: hasMultipleExecution,
											CustomTaskDetails:    map[string]interface{}{"bundleId": bundleId},
										}
										_, err := agentTaskService.Create(agentTask)
										if err != nil {
											logger.ServiceLogger.Error("[DeploymentService][AfterCreate]", err)
										}

									} else {
										agentTask := rest.AgentTaskRest{
											BaseEntityRefModelRest: rest.BaseEntityRefModelRest{
												RefId:    pkgId,
												RefModel: deployment.RefModel,
											},
											TaskType:             model.DEPLOYMENT.String(),
											AgentId:              assetId,
											DeploymentId:         deployment.Id,
											TaskStatus:           model.TaskWaiting.String(),
											HasMultipleExecution: hasMultipleExecution,
										}
										taskId, err := agentTaskService.Create(agentTask)
										if err == nil {
											service.DownloadPatchPackageFileForDeployment(taskId, pkgId, assetId, deployment.RefModel)
										}
									}
								}
							}
						}
						deployment.ComputerIds = append(deployment.ComputerIds, assetId)
						service.Update(deployment.Id, service.convertToRest(deployment))
					}
				}
			}
		}
	}
}
