package rest

import (
	"deployment/common"
	"deployment/model"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type PatchRest struct {
	BaseEntityRest
	Title                      string                `json:"title"`
	OsPlatform                 string                `json:"osPlatform"`
	OsArch                     string                `json:"osArch"`
	OsApplicationId            int64                 `json:"osApplicationId"`
	VendorId                   int64                 `json:"vendorId"`
	DownloadStatus             string                `json:"downloadStatus"`
	DownloadError              string                `json:"downloadError"`
	DownloadSize               int64                 `json:"downloadSize"`
	PatchApprovalStatus        string                `json:"patchApprovalStatus"`
	PatchTestStatus            string                `json:"patchTestStatus"`
	ApprovedOn                 int64                 `json:"approvedOn"`
	ApprovedBy                 int64                 `json:"approvedBy"`
	Description                string                `json:"description"`
	AffectedProducts           []int64               `json:"affectedProducts"`
	Tags                       []string              `json:"tags"`
	DownloadOn                 int64                 `json:"downloadOn"`
	BulletinId                 string                `json:"bulletinId"`
	CVENumber                  string                `json:"cveNumber"`
	KbId                       string                `json:"kbId"`
	PatchSeverity              string                `json:"patchSeverity"`
	PatchUpdateCategory        string                `json:"patchUpdateCategory"`
	SupportUrl                 string                `json:"supportUrl"`
	LanguageSupported          []int64               `json:"languageSupported"`
	RebootBehaviour            string                `json:"rebootBehaviour"`
	IsUninstallable            bool                  `json:"isUninstallable"`
	HasSupersededUpdates       bool                  `json:"hasSupersededUpdates"`
	IsSuperseded               bool                  `json:"isSuperseded"`
	DownloadFileDetails        []model.PatchFileData `json:"downloadFileDetails"`
	UUID                       string                `json:"uuid"`
	Status                     string                `json:"status"`
	Source                     string                `json:"source"`
	ReleaseDate                int64                 `json:"releaseDate"`
	ProductType                string                `json:"productType"`
	MissingEndpoints           int64                 `json:"missingEndpoints"`
	InstalledEndpoints         int64                 `json:"installedEndpoints"`
	IgnoredEndpoints           int64                 `json:"ignoredEndpoints"`
	IsThirdParty               bool                  `json:"isThirdParty"`
	InstallCommand             string                `json:"installCommand"`
	UninstallCommand           string                `json:"uninstallCommand"`
	UpgradeCommand             string                `json:"upgradeCommand"`
	AtLeastOneFileInstallation bool                  `json:"atLeastOneFileInstallation"`
	PackageNames               []string              `json:"packageNames"`
	Rollback                   string                `json:"rollback"`
}

func ConvertJsonToPatchRest(w http.ResponseWriter, r *http.Request, patchRest PatchRest) (PatchRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &patchRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid JSON format", http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprintf(w, jsonData)
		return patchRest, err
	}

	v := validator.New()
	err = v.Struct(patchRest)
	if err != nil {
		for _, err := range err.(validator.ValidationErrors) {
			jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Validation error on field %s, Expected values : %s", err.Field(), err.Param()), http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			fmt.Fprintf(w, jsonData)
			break
		}
	}

	var patchMap map[string]interface{}
	json.Unmarshal(body, &patchMap)
	patchRest.PatchMap = patchMap

	return patchRest, err
}
