package rest

type RedhatPatchRest struct {
	BaseEntityRest
	PkgId             string `json:"pkgId"`
	PkgName           string `json:"pkgName"`
	Title             string `json:"title"`
	Version           string `json:"version"`
	ReleaseVersion    string `json:"releaseVersion"`
	Arch              string `json:"arch"`
	Distribution      string `json:"distribution"`
	Severity          string `json:"severity"`
	BulletinId        string `json:"bulletinId"`
	ChangeLog         string `json:"changeLog"`
	DependenciesPkgId string `json:"dependenciesPkgId"`
	CVEIds            string `json:"cveIds"`
	ReleaseDate       int64  `json:"releaseDate"`
	LastModifiedDate  int64  `json:"lastModifiedDate"`
	SrcPkgName        string `json:"srcPkgName"`
	NameWithVersion   string `json:"nameWithVersion"`
	Dependencies      string `json:"dependencies"`
	DownloadUrl       string `json:"downloadUrl"`
	Size              int64  `json:"size"`
	Checksum          string `json:"checksum"`
	ChecksumType      string `json:"checksumType"`
	Location          string `json:"location"`
	Summary           string `json:"summary"`
	Description       string `json:"description"`
	Packager          string `json:"packager"`
	Vendor            string `json:"vendor"`
	License           string `json:"license"`
	Group             string `json:"group"`
	BuildHost         string `json:"buildHost"`
	HeaderRange       string `json:"headerRange"`
	Provides          string `json:"provides"`
	Requires          string `json:"requires"`
	Conflicts         string `json:"conflicts"`
	Obsoletes         string `json:"obsoletes"`
	Files             string `json:"files"`
	PackageFullName   string `json:"packageFullName"`
}
