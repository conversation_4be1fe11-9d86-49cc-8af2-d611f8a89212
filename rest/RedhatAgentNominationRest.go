package rest

import (
	"deployment/common"
	"deployment/logger"
	"encoding/json"
	"fmt"
	"github.com/go-playground/validator/v10"
	"net/http"
)

type RedhatAgentNominationRest struct {
	BaseEntityRest
	LastSyncTime     int64             `json:"lastSyncTime"`
	AssetId          int64             `json:"assetId"`
	LastRepoSyncDate int64             `json:"lastRepoSyncDate"`
	RedhatOsVariant  string            `json:"redhatOsVariant" validate:"omitempty,oneof=server workstation"`
	Status           string            `json:"status" validate:"omitempty,oneof=pending inprogress success failed"`
	Schedule         PatchScheduleRest `json:"schedule"`
}

func ConvertJsonToRedhatAgentNominationRest(w http.ResponseWriter, r *http.Request, redhatAgentNominationRest RedhatAgentNominationRest) (RedhatAgentNominationRest, error) {
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &redhatAgentNominationRest)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.<PERSON><PERSON><PERSON>(), http.StatusBadRequest))
		w.<PERSON>rite<PERSON>eader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			return redhatAgentNominationRest, err
		}
		return redhatAgentNominationRest, err
	}
	var patchMap map[string]interface{}
	err = json.Unmarshal(body, &patchMap)
	if err != nil {
		logger.ServiceLogger.Error("[ConvertJsonToRedhatAgentNominationRest]", err)
	}

	validate := validator.New()
	err = validate.Struct(redhatAgentNominationRest)
	redhatAgentNominationRest.PatchMap = patchMap
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			return redhatAgentNominationRest, err
		}
		return redhatAgentNominationRest, err
	}
	return redhatAgentNominationRest, nil
}
