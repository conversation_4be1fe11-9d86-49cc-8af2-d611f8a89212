package handler

import (
	"deployment/cache"
	"deployment/common"
	"deployment/constant"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
)

type AgentHandler struct {
	Service *service.AgentService
}

func GetAgentHandler() *AgentHandler {
	return &AgentHandler{
		Service: service.NewAgentService(),
	}
}

func readRepoFileWithUrlReplacement(osVariant, rhelVersion, serverUrl string) string {
	fileName := fmt.Sprintf("redhat%s.repo", rhelVersion)
	filePath := common.PrepareFilePath(common.FileDirectoryPath(), "repos", "redhat-repo", osVariant, fileName)

	if fileContent, err := os.ReadFile(filePath); err == nil {
		return strings.ReplaceAll(string(fileContent), "${downloadurl}", serverUrl+"/patchmirror")
	} else {
		logger.ServiceLogger.Error("Error reading repo file:", err)
		return ""
	}
}

func (h AgentHandler) AgentRefreshHandler(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{}
	var request map[string]interface{}

	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentRefreshHandler]", err)
		}
		return
	}

	if !cache.IsValidLicense() {
		jsonData, _ := common.RestToJson(w, common.Error("license expired.", http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentRefreshHandler]", err)
		}
		return
	}

	assetId := int64(request["assetId"].(float64))
	var taskTypeList []int64
	taskTypeList = append(taskTypeList, int64(model.SYSTEM_ACTION), int64(model.APPLICATION_CONTROL_POLICY), int64(model.COMPLIANCE_TEST))
	agentTaskService := service.NewAgentTaskService()
	allDeploymentTasks, _ := agentTaskService.GetAllAgentTaskBySearch(rest.SearchFilter{
		Qualification: []rest.Qualification{
			{
				Column:    "agent_id",
				Operator:  "Equals",
				Value:     assetId,
				Condition: "and",
			},
			{
				Column:    "task_status",
				Operator:  "Equals",
				Value:     int64(model.TaskReadyToDeploy),
				Condition: "and",
			},
			{
				Column:    "taskType",
				Operator:  "not_in",
				Value:     taskTypeList,
				Condition: "and",
			},
		},
	})
	response["task"] = allDeploymentTasks
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": response,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentRefreshHandler]", err)
		return
	}
}

func (h AgentHandler) AgentRefreshHandlerV2(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{}
	var request map[string]interface{}

	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentRefreshHandler1]", err)
		}
		return
	}

	if !cache.IsValidLicense() {
		jsonData, _ := common.RestToJson(w, common.Error("license expired.", http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentRefreshHandler]", err)
		}
		return
	}

	var allDeploymentTasks []rest.AgentTaskRest
	assetId := int64(request["assetId"].(float64))
	agentTaskService := service.NewAgentTaskService()
	tasks := cache.GetAllTasksForAgent(assetId)
	if tasks != nil {
		for _, task := range tasks {
			allDeploymentTasks = append(allDeploymentTasks, agentTaskService.ConvertToRest(task.(model.AgentTask)))
		}
	}
	response["task"] = allDeploymentTasks
	if allDeploymentTasks != nil && len(allDeploymentTasks) > 0 {
		result := map[int64]interface{}{}
		for _, agentTask := range allDeploymentTasks {
			if agentTask.TaskType != model.APPLICATION_CONTROL_POLICY.String() && agentTask.TaskType != model.COMPLIANCE_TEST.String() && agentTask.TaskType != model.QUICK_CHECK_TEST.String() && agentTask.TaskType != model.REDHAT_AGENT_NOMINATION.String() {
				taskDetails := agentTaskResolverHandler(agentTask, assetId)
				if len(taskDetails) > 0 {
					result[agentTask.Id] = taskDetails[agentTask.Id]
				}
				taskDetails = nil
			}
		}
		if len(result) > 0 {
			response["taskDetails"] = result
		}
	}
	jsonData, _ := common.RestToBytes(w, map[string]interface{}{
		"result": response,
	})
	_, err = w.Write(jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentRefreshHandler1]", err)
		return
	}
}

func (h AgentHandler) AgentSystemActionRefreshHandler(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{}
	var request map[string]interface{}

	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentSystemActionRefreshHandler]", err)
		}
		return
	}

	if !cache.IsValidLicense() {
		jsonData, _ := common.RestToJson(w, common.Error("license expired.", http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentRefreshHandler]", err)
		}
		return
	}

	assetId := int64(request["assetId"].(float64))
	agentTaskService := service.NewAgentTaskService()
	var taskTypeList []int64
	taskTypeList = append(taskTypeList, int64(model.SYSTEM_ACTION), int64(model.APPLICATION_CONTROL_POLICY), int64(model.COMPLIANCE_TEST))
	agentTasks, _ := agentTaskService.GetAllAgentTaskBySearch(rest.SearchFilter{
		Qualification: []rest.Qualification{
			{
				Column:    "agent_id",
				Operator:  "Equals",
				Value:     assetId,
				Condition: "and",
			},
			{
				Column:    "task_status",
				Operator:  "Equals",
				Value:     int64(model.TaskReadyToDeploy),
				Condition: "and",
			},
			{
				Column:    "taskType",
				Operator:  "in",
				Value:     taskTypeList,
				Condition: "and",
			},
		},
	})
	response["task"] = agentTasks
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": response,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentSystemActionRefreshHandler]", err)
	}
}

func (h AgentHandler) AgentTaskResolverHandler(w http.ResponseWriter, r *http.Request) {
	result := map[int64]interface{}{}
	var request map[string]interface{}
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentTaskResolverHandler]", err)
		}
		return
	}
	assetId := int64(request["assetId"].(float64))
	taskIds := request["taskId"]
	agentTaskService := service.NewAgentTaskService()
	packageService := service.NewPackageService()
	configurationService := service.NewConfigurationService()
	complianceService := service.NewComplianceService()
	if len(taskIds.([]interface{})) > 0 {
		ids := make([]int64, len(taskIds.([]interface{})))
		for i, id := range taskIds.([]interface{}) {
			ids[i] = int64(id.(float64)) // 'f' means decimal, 2 is precision, 64 is for float64
		}
		filter := rest.SearchFilter{
			Qualification: []rest.Qualification{
				rest.BuildQualification("id", "in", ids, "AND"),
			},
		}
		agentTasks, _ := agentTaskService.GetAgentTasks(filter)
		if agentTasks != nil && len(agentTasks) > 0 {
			for _, agentTask := range agentTasks {
				taskId := agentTask.Id
				logger.ServiceLogger.Info("Resolve task for asset : ", assetId, ", and task Id :", taskId)
				response := map[string]interface{}{}
				if agentTask.TaskType == model.HOLD_DEPLOYMENT_EXECUTION.String() {
					deploymentService := service.NewDeploymentService()
					deployment, err := deploymentService.GetDeployment(agentTask.DeploymentId, false)
					if err == nil && (deployment.DeploymentStage == model.Resume.String() || deployment.DeploymentStage == model.Pause.String()) {
						response["deployment"] = deployment
					}
				} else if agentTask.TaskType == model.DEPLOYMENT.String() {
					deploymentService := service.NewDeploymentService()
					deployment, err := deploymentService.GetDeployment(agentTask.DeploymentId, false)
					if err == nil {
						response["deployment"] = deployment
					}

					if err == nil && !(deployment.DeploymentStage == model.Resume.String() || deployment.DeploymentStage == model.Pause.String()) {
						if agentTask.RefModel == common.PACKAGE.String() {
							pkg, err := packageService.GetPackage(agentTask.RefId, false)
							if err == nil {
								var pkgFilePathList []rest.FileMetaDataRest
								isFileServerAvailable := false
								assetRest, err := service.NewAgentService().GetAssetMetaDataById(assetId)
								if err == nil {
									_, err := service.NewFileServerConfigService().Repository.GetByLocation(assetRest.Location, false)
									if err == nil {
										isFileServerAvailable = true
										for _, filePatch := range pkg.PkgFilePathList {
											if filePatch.LocationId == assetRest.Location {
												pkgFilePathList = append(pkgFilePathList, filePatch)
											}
										}
									}
								}
								if isFileServerAvailable {
									pkg.PkgFilePathList = pkgFilePathList
									if len(pkgFilePathList) > 0 {
										pkg.PkgFilePath = pkgFilePathList[0]
									}
								}
								response["package"] = pkg
							} else {
								logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", taskId, err.Error())
							}
						} else if agentTask.RefModel == common.CONFIGURATION.String() {
							config, err := configurationService.GetConfiguration(agentTask.RefId, false)
							if err == nil {
								response["configuration"] = config
							} else {
								logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", taskId, err.Error())
							}
						} else if agentTask.RefModel == common.COMPLIANCES.String() {
							config, err := complianceService.GetCompliance(agentTask.RefId, false)
							if err == nil {
								response["compliance"] = config
							} else {
								logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", taskId, err.Error())
							}
						} else if strings.ToLower(agentTask.RefModel) == common.PATCH.String() {
							patch, err := service.NewPatchService().GetPatch(agentTask.RefId, false)
							if err == nil {
								var fileDetails []model.PatchFileData
								isFileServerAvailable := false
								assetRest, err := service.NewAgentService().GetAssetMetaDataById(assetId)
								if err == nil {
									_, err := service.NewFileServerConfigService().Repository.GetByLocation(assetRest.Location, false)
									if err == nil {
										isFileServerAvailable = true
										for _, detail := range patch.DownloadFileDetails {
											if detail.LocationId == assetRest.Location {
												fileDetails = append(fileDetails, detail)
											}
										}
									}
								}
								if !isFileServerAvailable {
									for _, detail := range patch.DownloadFileDetails {
										if detail.LocationId == 0 {
											fileDetails = append(fileDetails, detail)
										}
									}
								}
								patch.DownloadFileDetails = fileDetails
								response["patch"] = patch
							} else {
								logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", taskId, err.Error())
							}
						}

						policyService := service.NewDeploymentPolicyService()
						policy, err := policyService.GetDeploymentPolicy(deployment.DeploymentPolicyId, false)
						if err == nil {
							response["policy"] = policy
						}
					}
				} else if agentTask.TaskType == model.PATCH_SCAN.String() {
					var commandMap map[string]interface{}

					asset, err := service.NewAgentService().GetAssetMetaDataById(agentTask.AgentId)
					if err != nil {
						continue
					}
					commandString := constant.PatchScanQueries[asset.Platform]
					if commandString != "" {
						err = json.Unmarshal([]byte(commandString), &commandMap)
						if err != nil {
							logger.ServiceLogger.Error("[AgentTaskResolverHandler]", err)
						}
					}

					response["commands"] = commandMap

					policyService := service.NewDeploymentPolicyService()
					policy, err := policyService.GetDeploymentPolicy(constant.DefaultInstantDeploymentPolicyId, false)
					if err == nil {
						response["policy"] = policy
					}

				} else if agentTask.TaskType == model.QUERY_EXECUTION.String() {
					response["commands"] = agentTask.CustomTaskDetails

					policyService := service.NewDeploymentPolicyService()
					policy, err := policyService.GetDeploymentPolicy(constant.DefaultInstantDeploymentPolicyId, false)
					if err == nil {
						response["policy"] = policy
					}
				} else if agentTask.TaskType == model.SYSTEM_ACTION.String() {
					if agentTask.RefModel == common.CONFIGURATION.String() {
						config, err := configurationService.GetConfiguration(agentTask.RefId, false)
						if err == nil {
							if config.ConfigurationActions != nil && agentTask.CustomTaskDetails != nil && len(agentTask.CustomTaskDetails) > 0 {
								context := agentTask.CustomTaskDetails["context"]
								if context != nil {
									var actions []rest.ConfigurationActionRest
									if ctxList, ok := context.([]interface{}); ok {
										for _, ctx := range ctxList {
											if ctxMap, ok := ctx.(map[string]interface{}); ok {
												for _, action := range config.ConfigurationActions {
													for key, val := range ctxMap {
														action.Command = strings.ReplaceAll(action.Command, "{$"+key+"}", fmt.Sprint(val))
													}
													actions = append(actions, action)
												}
											}
										}
									}
									if len(actions) > 0 {
										config.ConfigurationActions = actions
									}
								}
							}
							response["configuration"] = config
						} else {
							logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", taskId, err.Error())
						}
					}
				}
				result[taskId] = response
			}
		}
	}
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": result,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentTaskResolverHandler]", err)
		return
	}
}

func agentTaskResolverHandler(agentTask rest.AgentTaskRest, assetId int64) map[int64]interface{} {
	result := map[int64]interface{}{}
	packageService := service.NewPackageService()
	configurationService := service.NewConfigurationService()
	complianceService := service.NewComplianceService()
	deploymentService := service.NewDeploymentService()
	response := map[string]interface{}{}
	if agentTask.TaskType == model.HOLD_DEPLOYMENT_EXECUTION.String() {
		deployment, err := deploymentService.GetDeployment(agentTask.DeploymentId, false)
		if err == nil && (deployment.DeploymentStage == model.Resume.String() || deployment.DeploymentStage == model.Pause.String()) {
			response["deployment"] = deployment
		}
	} else if agentTask.TaskType == model.DEPLOYMENT.String() {
		deployment, err := deploymentService.GetDeployment(agentTask.DeploymentId, false)
		if err == nil {
			response["deployment"] = deployment
		}
		if err == nil && !(deployment.DeploymentStage == model.Resume.String() || deployment.DeploymentStage == model.Pause.String()) {
			if agentTask.RefModel == common.PACKAGE.String() {
				pkg, err := packageService.GetPackage(agentTask.RefId, false)
				if err == nil {
					var pkgFilePathList []rest.FileMetaDataRest
					isFileServerAvailable := false
					assetRest, err := service.NewAgentService().GetAssetMetaDataById(assetId)
					if err == nil {
						_, err := service.NewFileServerConfigService().Repository.GetByLocation(assetRest.Location, false)
						if err == nil {
							isFileServerAvailable = true
							for _, filePatch := range pkg.PkgFilePathList {
								if filePatch.LocationId == assetRest.Location {
									pkgFilePathList = append(pkgFilePathList, filePatch)
								}
							}
						}
					}
					if isFileServerAvailable {
						pkg.PkgFilePathList = pkgFilePathList
						if len(pkgFilePathList) > 0 {
							pkg.PkgFilePath = pkgFilePathList[0]
						}
					}
					response["package"] = pkg
				} else {
					logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", agentTask.Id, err.Error())
				}
			} else if agentTask.RefModel == common.CONFIGURATION.String() {
				config, err := configurationService.GetConfiguration(agentTask.RefId, false)
				if err == nil {
					response["configuration"] = config
				} else {
					logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", agentTask.Id, err.Error())
				}
			} else if agentTask.RefModel == common.COMPLIANCES.String() {
				config, err := complianceService.GetCompliance(agentTask.RefId, false)
				if err == nil {
					response["compliance"] = config
				} else {
					logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", agentTask.Id, err.Error())
				}
			} else if strings.ToLower(agentTask.RefModel) == common.PATCH.String() {
				patch, err := service.NewPatchService().GetPatch(agentTask.RefId, false)
				if err == nil {
					var fileDetails []model.PatchFileData
					isFileServerAvailable := false
					assetRest, err := service.NewAgentService().GetAssetMetaDataById(assetId)
					if err == nil {
						_, err := service.NewFileServerConfigService().Repository.GetByLocation(assetRest.Location, false)
						if err == nil {
							isFileServerAvailable = true
							for _, detail := range patch.DownloadFileDetails {
								if detail.LocationId == assetRest.Location {
									fileDetails = append(fileDetails, detail)
								}
							}
						}
					}
					if !isFileServerAvailable {
						for _, detail := range patch.DownloadFileDetails {
							if detail.LocationId == 0 {
								fileDetails = append(fileDetails, detail)
							}
						}
					}
					patch.DownloadFileDetails = fileDetails
					response["patch"] = patch
				} else {
					logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", agentTask.Id, err.Error())
				}
			}

			policyService := service.NewDeploymentPolicyService()
			policy, err := policyService.GetDeploymentPolicy(deployment.DeploymentPolicyId, false)
			if err == nil {
				response["policy"] = policy
			}
		}
	} else if agentTask.TaskType == model.PATCH_SCAN.String() {

		assetDetail, _ := cache.AssetCache.Get(strconv.FormatInt(assetId, 10))
		platform := assetDetail["platform_version"].(string)
		if strings.Contains(platform, "Red Hat") {
			serverUrl := common.ServerUrl()
			var content string

			osVariant := "server"
			if strings.Contains(strings.ToLower(platform), "workstation") {
				osVariant = "workstation"
			}

			rhelVersion := assetDetail["major"].(string)
			content = readRepoFileWithUrlReplacement(osVariant, rhelVersion, serverUrl)
			response["repoBlock"] = content
			commandFilePath := common.PrepareFilePath(common.FileDirectoryPath(), "patch-queries", "linux-redhat-patch-queries.yml")
			commandMap, err := common.ReadYamlFile(commandFilePath)
			if err != nil {
				logger.ServiceLogger.Error("Error reading command file:", err)
			} else {
				response["commandBlock"] = commandMap
			}
		} else {
			asset, _ := service.NewAgentService().GetAssetMetaDataById(agentTask.AgentId)
			var commandMap map[string]interface{}
			commandString := constant.PatchScanQueries[asset.Platform]
			if commandString != "" {
				err := json.Unmarshal([]byte(commandString), &commandMap)
				if err != nil {
					logger.ServiceLogger.Error("[agentTaskResolverHandler]", err)
				}
			}
			response["commands"] = commandMap
		}
		policyService := service.NewDeploymentPolicyService()
		policy, err := policyService.GetDeploymentPolicy(constant.DefaultInstantDeploymentPolicyId, false)
		if err == nil {
			response["policy"] = policy
		}

	} else if agentTask.TaskType == model.QUERY_EXECUTION.String() {
		response["commands"] = agentTask.CustomTaskDetails
		policyService := service.NewDeploymentPolicyService()
		policy, err := policyService.GetDeploymentPolicy(constant.DefaultInstantDeploymentPolicyId, false)
		if err == nil {
			response["policy"] = policy
		}
	} else if agentTask.TaskType == model.SYSTEM_ACTION.String() && agentTask.RefModel == common.CONFIGURATION.String() {
		config, err := configurationService.GetConfiguration(agentTask.RefId, false)
		if err == nil {
			if config.ConfigurationActions != nil && agentTask.CustomTaskDetails != nil && len(agentTask.CustomTaskDetails) > 0 {
				context := agentTask.CustomTaskDetails["context"]
				if context != nil {
					if ctxList, ok := context.([]interface{}); ok {
						var actions []rest.ConfigurationActionRest
						for _, ctx := range ctxList {
							if ctxMap, ok := ctx.(map[string]interface{}); ok {
								for _, action := range config.ConfigurationActions {
									for key, val := range ctxMap {
										action.Command = strings.ReplaceAll(action.Command, "{$"+key+"}", fmt.Sprint(val))
									}
									actions = append(actions, action)
								}
							}
						}
						if len(actions) > 0 {
							config.ConfigurationActions = actions
						}
					}
				}
			}

			response["configuration"] = config
		} else {
			logger.ServiceLogger.Error("Error Resolve task for asset : ", assetId, ", and task Id :", agentTask.Id, err.Error())
		}
	}
	if len(response) > 0 {
		result[agentTask.Id] = response
	}

	return result
}

func (h AgentHandler) AgentSearchByScope(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{}
	var request map[string]string
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentSearchByScope]", err)
		}
		return
	}

	scopeId, _ := common.ConvertToLong(request["scope"])
	var assets []int64
	var platformVersion []string
	if val, ok := request["assets"]; ok && val != "" {
		strSlice := strings.Split(val, ",")
		for _, s := range strSlice {
			num, err := strconv.ParseInt(s, 10, 64)
			if err != nil {
				fmt.Println("Error:", err)
				return
			}
			assets = append(assets, num)
		}
	}

	if val, ok := request["platform_version"]; ok && val != "" {
		platformVersion = append(platformVersion, strings.Split(val, ",")...)
	}
	assetIds, _ := h.Service.GetAllAssetIdsByScope(model.AgentScopeFilter{
		Scope:  scopeId,
		Assets: assets,
	})
	response["assetIds"] = assetIds
	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": response,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentSearchByScope]", err)
	}
}

func (h AgentHandler) AgentSelfServiceDeploymentHandler(w http.ResponseWriter, r *http.Request) {
	var request map[string]interface{}
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentSelfServiceDeploymentHandler]", err)
		}
		return
	}

	deploymentRest := rest.DeploymentRest{}
	if val, ok := request["refModel"]; ok && val != nil {
		if common.PACKAGE.String() == val.(string) {
			deploymentRest.RefModel = common.PACKAGE.String()
		} else if common.CONFIGURATION.String() == val.(string) {
			deploymentRest.RefModel = common.CONFIGURATION.String()
		} else {
			jsonData, _ := common.RestToJson(w, common.Error("Invalid Input Data", http.StatusInternalServerError))
			w.WriteHeader(http.StatusInternalServerError)
			_, err = fmt.Fprintf(w, jsonData)
			if err != nil {
				logger.ServiceLogger.Error("[AgentSelfServiceDeploymentHandler]", err)
			}
			return
		}

		assetId := int64(request["assetId"].(float64))
		refId := int64(request["refId"].(float64))
		deploymentType := request["deploymentType"].(string)

		deploymentRest.RefIds = []int64{refId}
		deploymentRest.Scope = int64(model.SpecificAssets)
		deploymentRest.Assets = []int64{assetId}
		deploymentRest.DisplayName = "Self service deployment"
		deploymentRest.Name = "Self service deployment"
		deploymentRest.Description = "Self service deployment"
		deploymentRest.DeploymentType = deploymentType
		deploymentRest.IsPkgSelectAsBundle = false
		deploymentRest.DeploymentStage = "initiated"
		deploymentRest.RetryCount = 3
		deploymentRest.DeploymentPolicyId = 1
		deploymentRest.IsSelfServiceDeployment = true

		_, err := service.NewDeploymentService().Create(deploymentRest)
		if err != nil {
			logger.ServiceLogger.Error("[AgentSelfServiceDeploymentHandler]", err)
			return
		}
	} else {
		jsonData, _ := common.RestToJson(w, common.Error("Invalid Input Data", http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentSelfServiceDeploymentHandler]", err)
		}
		return
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": "success",
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentSelfServiceDeploymentHandler]", err)
	}

}

func (h AgentHandler) AgentSelfServiceDeploymentSearchHandler(w http.ResponseWriter, r *http.Request) {
	var request map[string]interface{}
	var responsePage rest.ListResponseRest
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentSelfServiceDeploymentSearchHandler]", err)
		}
		return
	}

	assetId := int64(request["assetId"].(float64))

	deploymentService := service.NewDeploymentService()
	deploymentList, err := deploymentService.Repository.GetAllSelfServiceDeploymentsByAssetId(assetId)
	if err == nil && deploymentList != nil && len(deploymentList) > 0 {
		responsePage.TotalCount = len(deploymentList)
		responsePage.ObjectList = deploymentService.ConvertListToRest(deploymentList)
	}

	jsonData, _ := common.RestToJson(w, responsePage)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentSelfServiceDeploymentSearchHandler]", err)
	}
}

func (h AgentHandler) AgentSearchHandler(w http.ResponseWriter, r *http.Request) {
	var request map[string]interface{}
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &request)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[AgentSearchHandler]", err)
		}
		return
	}

	uuid := request["uuid"]
	response := map[string]interface{}{}
	assetDetailsMap := cache.AssetCache.GetAll()
	if uuidStr, ok := uuid.(string); ok && assetDetailsMap != nil && len(assetDetailsMap) > 0 {
		for assetId, assetDetails := range assetDetailsMap {
			if assetDetails == nil {
				continue
			}

			if val, ok := assetDetails["system_uuid"].(string); ok && strings.EqualFold(uuidStr, val) {
				if id, err := strconv.ParseInt(assetId, 10, 64); err == nil {
					response["assetId"] = id
				}
				break
			}

			if val, ok := assetDetails["uuid"].(string); ok && strings.EqualFold(uuidStr, val) {
				if id, err := strconv.ParseInt(assetId, 10, 64); err == nil {
					response["assetId"] = id
				}
				break
			}
		}
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": response,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[AgentSearchHandler]", err)
	}
}
