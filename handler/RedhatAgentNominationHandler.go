package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/rest"
	"deployment/service/redhat"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
)

type RedhatAgentNominationHandler struct {
	Service *redhat.RedhatAgentNominationService
}

func NewRedhatAgentNominationHandler() *RedhatAgentNominationHandler {
	return &RedhatAgentNominationHandler{
		Service: redhat.NewRedhatAgentNominationService(),
	}
}

func (handler RedhatAgentNominationHandler) GetRedhatAgentNominationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting id from api path: "+err.Error(), http.StatusBadRequest))
		w.<PERSON>rite<PERSON>eader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetRedhatAgentNominationHandler] ", err)
			return
		}
		return
	}

	nominationRest, err := handler.Service.GetRedhatAgentNomination(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetRedhatAgentNominationHandler] ", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, nominationRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetRedhatAgentNominationHandler] ", err)
		return
	}
}

func (handler RedhatAgentNominationHandler) UpdateRedhatAgentNominationHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idString, _ := vars["id"]
	id, err := common.ConvertToLong(idString)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while getting id from api path: "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateRedhatAgentNominationHandler] ", err)
			return
		}
		return
	}

	var nominationRest rest.RedhatAgentNominationRest
	nominationRest, err = rest.ConvertJsonToRedhatAgentNominationRest(w, r, nominationRest)
	if err != nil {
		return
	}

	// Validate that only assetId and status are being updated
	if nominationRest.PatchMap != nil {
		allowedFields := map[string]bool{"assetId": true, "status": true}
		for field := range nominationRest.PatchMap {
			if !allowedFields[field] {
				jsonData, _ := common.RestToJson(w, common.Error(fmt.Sprintf("Field '%s' is not allowed to be updated via API. Only 'assetId' and 'status' fields can be updated.", field), http.StatusBadRequest))
				w.WriteHeader(http.StatusBadRequest)
				_, err = fmt.Fprintf(w, jsonData)
				if err != nil {
					logger.ServiceLogger.Error("[UpdateRedhatAgentNominationHandler] ", err)
					return
				}
				return
			}
		}
	}

	updated, updateErr := handler.Service.UpdateRedhatAgentNomination(id, nominationRest)
	if updateErr.Message != "" {
		jsonData, _ := common.RestToJson(w, common.Error("Error while updating RedhatAgentNomination: "+updateErr.Message, updateErr.Code))
		w.WriteHeader(updateErr.Code)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[UpdateRedhatAgentNominationHandler] ", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": updated,
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[UpdateRedhatAgentNominationHandler] ", err)
		return
	}
}

func (handler RedhatAgentNominationHandler) GetAllRedhatAgentNominationHandler(w http.ResponseWriter, r *http.Request) {
	var searchFilter rest.SearchFilter
	searchFilter, err := rest.ConvertJsonToSearchFilter(w, r, searchFilter)
	if err != nil {
		return
	}

	if searchFilter.SortBy == "" {
		searchFilter.SortBy = "redhat_os_variant"
	}

	nominationRest, err := handler.Service.GetAllRedhatAgentNomination(searchFilter)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAllRedhatAgentNominationHandler] ", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, nominationRest)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAllRedhatAgentNominationHandler] ", err)
		return
	}
}
