package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"deployment/service"
	"encoding/json"
	"fmt"
	"net/http"
)

type AssetHandler struct {
	Service *service.AssetService
}

func NewAssetHandler() *AssetHandler {
	return &AssetHandler{
		Service: service.NewAssetService(),
	}
}

func (handler AssetHandler) GetAssetComplianceHeatMapHandler(w http.ResponseWriter, r *http.Request) {
	id := common.ParseIdFromApiPath(w, r)
	if id == 0 {
		return
	}

	heatMap, err := handler.Service.GetAssetComplianceHeatMap(id)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAssetComplianceHeatMapHandler]", err)
			return
		}
		return
	}

	jsonData, _ := common.RestToJson(w, heatMap)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAssetComplianceHeatMapHandler]", err)
		return
	}
}

func (handler AssetHandler) GetAssetComplianceHistoryHandler(w http.ResponseWriter, r *http.Request) {
	assetId := common.ParseIdFromApiPath(w, r)
	if assetId == 0 {
		return
	}
	var parameters map[string]interface{}
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &parameters)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAssetComplianceHistoryHandler]", err)
			return
		}
		return
	}

	heatMap, err := handler.Service.GetAssetComplianceHistory(assetId, common.ConvertToInt64(parameters["deploymentId"]), common.ConvertToInt64(parameters["count"]))
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error(err.Error(), http.StatusInternalServerError))
		w.WriteHeader(http.StatusInternalServerError)
		_, err = fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAssetComplianceHistoryHandler]", err)
			return
		}
		return
	}
	jsonData, _ := common.RestToJson(w, heatMap)
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAssetComplianceHistoryHandler]", err)
		return
	}
}

func (handler AssetHandler) TestQuickCheck(w http.ResponseWriter, r *http.Request) {
	var parameters map[string]interface{}
	body := common.GetRequestBody(r)
	err := json.Unmarshal(body, &parameters)
	if err != nil {
		jsonData, _ := common.RestToJson(w, common.Error("Error while test quick checks : "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[TestQuickCheck]", err)
		}
		return
	}

	assetId := common.ParseIdFromApiPath(w, r)
	if assetId == 0 {
		return
	}

	agentTask := rest.AgentTaskRest{}
	agentTask.AgentId = assetId
	agentTask.TaskStatus = model.TaskReadyToDeploy.String()
	agentTask.TaskType = model.QUICK_CHECK_TEST.String()
	agentTask.CustomTaskDetails = parameters
	taskId, _ := NewAgentTaskHandler().Service.Create(agentTask)

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": map[string]interface{}{
			"id": taskId,
		},
	})
	_, err = fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[TestQuickCheck]", err)
	}
}

func (handler AssetHandler) GetAssetByOsVariantHandler(w http.ResponseWriter, r *http.Request) {
	// Parse OS variant from query parameter
	osVariant := r.URL.Query().Get("osVariant")
	if osVariant == "" {
		jsonData, _ := common.RestToJson(w, common.Error("osVariant query parameter is required", http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, err := fmt.Fprintf(w, jsonData)
		if err != nil {
			logger.ServiceLogger.Error("[GetAssetByOsVariantHandler]", err)
		}
		return
	}

	// Use AgentService to get assets by OS variant
	agentService := service.NewAgentService()
	assetMap := agentService.GetAssetByOsVariant(osVariant)

	jsonData, _ := common.RestToJson(w, map[string]interface{}{
		"result": assetMap,
	})
	_, err := fmt.Fprintf(w, jsonData)
	if err != nil {
		logger.ServiceLogger.Error("[GetAssetByOsVariantHandler]", err)
	}
}
