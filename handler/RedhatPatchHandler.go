package handler

import (
	"deployment/common"
	"deployment/logger"
	"deployment/service/redhat"
	"fmt"
	"github.com/gorilla/mux"
	"net/http"
)

type RedhatPatchHandler struct {
	Service *redhat.RedhatPatchService
}

func NewRedhatPatchHandler() *RedhatPatchHandler {
	return &RedhatPatchHandler{
		Service: redhat.NewRedhatPatchService(),
	}
}

// UploadPatchMirrorFileHandler handles file upload requests for patch mirror
func (handler RedhatPatchHandler) UploadPatchMirrorFileHandler(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("UploadPatchMirrorFileHandler: Request received")

	// Parse multipart form with 1GB limit
	err := r.ParseMultipartForm(1024 << 20) // 1024 MB limit
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Failed to parse multipart form: %s", err.Error()))
		jsonData, _ := common.RestToJson(w, common.Error("Failed to parse form data: "+err.Error(), http.StatusBadRequest))
		w.<PERSON><PERSON><PERSON>(http.StatusBadRequest)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadPatchMirrorFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}

	// Get file from form
	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error getting file from form: %s", err.Error()))
		jsonData, _ := common.RestToJson(w, common.Error("File parameter is required: "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadPatchMirrorFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadPatchMirrorFileHandler] Error closing file: %s", closeErr.Error()))
		}
	}()

	// Get folder path from form
	folderPath := r.FormValue("folderpath")
	if folderPath == "" {
		logger.ServiceLogger.Error("Folder path parameter is missing")
		jsonData, _ := common.RestToJson(w, common.Error("Folder path parameter is required", http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadPatchMirrorFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}

	// Call service to upload file
	response, customErr := handler.Service.UploadPatchMirrorFile(fileHeader, file, folderPath)
	if customErr.Message != "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Error uploading patch mirror file: %s", customErr.Message))
		jsonData, _ := common.RestToJson(w, common.Error(customErr.Message, customErr.Code))
		w.WriteHeader(customErr.Code)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadPatchMirrorFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}

	// Return success response
	logger.ServiceLogger.Info("UploadPatchMirrorFileHandler: File uploaded successfully")
	jsonData, _ := common.RestToJson(w, response)
	w.WriteHeader(http.StatusOK)
	_, writeErr := fmt.Fprintf(w, jsonData)
	if writeErr != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("[UploadPatchMirrorFileHandler] Error writing response: %s", writeErr.Error()))
	}
}

// UploadCertificateFileHandler handles certificate file upload requests
func (handler RedhatPatchHandler) UploadCertificateFileHandler(w http.ResponseWriter, r *http.Request) {
	logger.ServiceLogger.Info("UploadCertificateFileHandler: Request received")

	// Get asset ID from path parameter
	vars := mux.Vars(r)
	assetIdString, exists := vars["assetid"]
	if !exists || assetIdString == "" {
		logger.ServiceLogger.Error("Asset ID parameter is missing from path")
		jsonData, _ := common.RestToJson(w, common.Error("Asset ID parameter is required", http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadCertificateFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}

	// Convert asset ID to int64
	assetId, err := common.ConvertToLong(assetIdString)
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Invalid asset ID: %s, Error: %s", assetIdString, err.Error()))
		jsonData, _ := common.RestToJson(w, common.Error("Invalid asset ID: "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadCertificateFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}

	// Parse multipart form with 1GB limit
	err = r.ParseMultipartForm(1024 << 20) // 1024 MB limit
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Failed to parse multipart form: %s", err.Error()))
		jsonData, _ := common.RestToJson(w, common.Error("Failed to parse form data: "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadCertificateFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}

	// Get file from form
	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("Error getting file from form: %s", err.Error()))
		jsonData, _ := common.RestToJson(w, common.Error("File parameter is required: "+err.Error(), http.StatusBadRequest))
		w.WriteHeader(http.StatusBadRequest)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadCertificateFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}
	defer func() {
		if closeErr := file.Close(); closeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadCertificateFileHandler] Error closing file: %s", closeErr.Error()))
		}
	}()

	// Call service to upload certificate file
	response, customErr := handler.Service.UploadCertificateFile(fileHeader, file, assetId)
	if customErr.Message != "" {
		logger.ServiceLogger.Error(fmt.Sprintf("Error uploading certificate file: %s", customErr.Message))
		jsonData, _ := common.RestToJson(w, common.Error(customErr.Message, customErr.Code))
		w.WriteHeader(customErr.Code)
		_, writeErr := fmt.Fprintf(w, jsonData)
		if writeErr != nil {
			logger.ServiceLogger.Error(fmt.Sprintf("[UploadCertificateFileHandler] Error writing response: %s", writeErr.Error()))
		}
		return
	}

	// Return success response
	logger.ServiceLogger.Info("UploadCertificateFileHandler: Certificate file uploaded successfully")
	jsonData, _ := common.RestToJson(w, response)
	w.WriteHeader(http.StatusOK)
	_, writeErr := fmt.Fprintf(w, jsonData)
	if writeErr != nil {
		logger.ServiceLogger.Error(fmt.Sprintf("[UploadCertificateFileHandler] Error writing response: %s", writeErr.Error()))
	}
}
