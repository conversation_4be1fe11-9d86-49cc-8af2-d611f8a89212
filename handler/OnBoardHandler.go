package handler

import (
	"context"
	"deployment/cache"
	"deployment/constant"
	"deployment/db"
	"deployment/logger"
	"deployment/model"
	"deployment/rest"
	"deployment/service"
	"deployment/service/redhat"
	"fmt"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/stdlib"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/dialect/pgdialect"
)

func SystemOnBoardService() {
	constant.CallContext = context.WithValue(context.Background(), constant.UserId, int64(0))

	ReloadCache()
	prepareAgentTaskCache()
	service.NewJobExecutorService().Init()
	go service.NewPackageService().CreateOOBPackages()
	go service.NewConfigurationService().CreateOOBConfiguration()
	go service.NewDeploymentPolicyService().CreateOOBDeploymentPolicy()
	go service.NewSystemActionService().CreateOOBSystemAction()
	go service.NewPatchPreferenceService().CreateOOBPreference()
	go redhat.NewRedhatAgentNominationService().CreateDefaultNominations()
}

func ReloadCache() {
	go prepareUserCache()
	go preparePermissionCache()
	go PrepareAssetCache()
	go retrieveServerSettings()
}

func ReloadUserCache() {
	go prepareUserCache()
	go preparePermissionCache()
	go retrieveServerSettings()
}

func PrepareAssetCache() {
	response := map[string]map[string]interface{}{}
	departmentAssets := map[string][]string{}
	config, err := pgx.ParseConfig(db.MainServerDBConnString)
	if err != nil {
		panic(err)
	}
	config.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol
	sqlDB := stdlib.OpenDB(*config)
	newDB := bun.NewDB(sqlDB, pgdialect.New())
	var queryResponse []map[string]interface{}
	err = newDB.NewRaw("SELECT * FROM tbl_asset WHERE archived = 0").Scan(context.Background(), &queryResponse)
	if err != nil {
		logger.ServiceLogger.Error("Error while Get_All_Asset :", err)
		return
	}

	astService := service.NewAssetService()
	for _, row := range queryResponse {
		if val, ok := row["archived"]; ok && val.(int64) == 0 {
			id := fmt.Sprintf("%d", row["id"])
			department := fmt.Sprintf("%d", row["department"])
			response[id] = row
			_, err = astService.GetAsset(row["id"].(int64))
			if err != nil {
				logger.ServiceLogger.Error("Error while PrepareAssetCache :", err)
			}

			if astList, ok := departmentAssets[department]; ok {
				var assetList []string
				assetList = append(assetList, id)
				assetList = append(assetList, astList...)
				departmentAssets[department] = assetList
			} else {
				departmentAssets[department] = []string{id}
			}
		}
	}

	cache.AssetCache.CleanAll()
	cache.DepartmentAssetCache.CleanAll()
	for id, row := range response {
		cache.AssetCache.Set(id, row)
	}
	for id, assetIds := range departmentAssets {
		cache.DepartmentAssetCache.Set(id, assetIds)
	}
	defer func(Connection *bun.DB) {
		err := Connection.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(newDB)
}

func prepareUserCache() {
	config, err := pgx.ParseConfig(db.MainServerDBConnString)
	if err != nil {
		panic(err)
	}
	config.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol
	sqlDB := stdlib.OpenDB(*config)
	newDB := bun.NewDB(sqlDB, pgdialect.New())
	var queryResponse []map[string]interface{}
	err = newDB.NewRaw("SELECT * FROM tbl_user").Scan(context.Background(), &queryResponse)
	if err != nil {
		logger.ServiceLogger.Error("Error while Get_All_User_Query : ", err)
		return
	}

	cache.UserCache.CleanAll()
	for _, user := range queryResponse {
		cache.UserCache.Set(user["id"].(int64), user)
	}

	defer func(Connection *bun.DB) {
		err := Connection.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(newDB)
}

func preparePermissionCache() {
	config, err := pgx.ParseConfig(db.MainServerDBConnString)
	if err != nil {
		panic(err)
	}
	config.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol
	sqlDB := stdlib.OpenDB(*config)
	newDB := bun.NewDB(sqlDB, pgdialect.New())
	var permissionQueryResponse []map[string]interface{}
	err = newDB.NewRaw("SELECT usr.id,  \"role\", per.capabilities  FROM tbl_user usr,tbl_user_permission per WHERE usr.\"role\" = per.id and usr.archived != 1 AND per.archived != 1").Scan(context.Background(), &permissionQueryResponse)
	if err != nil {
		logger.ServiceLogger.Error("Error while Get_All_User_Roles_Join_With_User : ", err)
		return
	}

	cache.UserPermissionCache.CleanAll()
	for _, permission := range permissionQueryResponse {
		cache.UserPermissionCache.Set(permission["id"].(int64), permission["capabilities"].(string))
	}
	defer func(Connection *bun.DB) {
		err := Connection.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(newDB)
}

func prepareAgentTaskCache() {
	agentTaskService := service.NewAgentTaskService()
	var taskStatusList []int64
	taskStatusList = append(taskStatusList, int64(model.TaskReadyToDeploy))
	agentTasks, _ := agentTaskService.GetAllTasks(rest.SearchFilter{
		Qualification: []rest.Qualification{
			{
				Column:    "task_status",
				Operator:  "in",
				Value:     taskStatusList,
				Condition: "and",
			},
		},
	})
	if agentTasks != nil && len(agentTasks) > 0 {
		for _, agentTask := range agentTasks {
			agentTaskService.PostOperation(&agentTask)
		}
	}
}

func retrieveServerSettings() {
	config, err := pgx.ParseConfig(db.MainServerDBConnString)
	if err != nil {
		panic(err)
	}
	config.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol
	sqlDB := stdlib.OpenDB(*config)
	newDB := bun.NewDB(sqlDB, pgdialect.New())
	var queryResponse []map[string]interface{}
	err = newDB.NewRaw("SELECT log_level FROM tbl_server_settings").Scan(context.Background(), &queryResponse)
	if err != nil {
		logger.ServiceLogger.Error("Error while Get_All_Asset :", err)
		return
	}

	for _, row := range queryResponse {
		if val, ok := row["log_level"]; ok && val.(int64) >= 0 {
			level := val.(int64)
			if level == 0 {
				logger.ChangeLogLeve("trace")
			} else if level == 1 {
				logger.ChangeLogLeve("debug")
			} else if level == 2 {
				logger.ChangeLogLeve("warn")
			} else if level == 3 {
				logger.ChangeLogLeve("info")
			}
		}
	}
	defer func(Connection *bun.DB) {
		err := Connection.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(newDB)
}
