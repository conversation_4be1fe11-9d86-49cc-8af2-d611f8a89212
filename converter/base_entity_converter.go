package converter

import (
	"deployment/model"
	"deployment/rest"
)

// ConvertToBaseEntityModel converts BaseEntityRest to BaseEntityModel
func ConvertToBaseEntityModel(rest rest.BaseEntityRest) model.BaseEntityModel {
	return model.BaseEntityModel{
		Id:          rest.Id,
		Name:        rest.Name,
		CreatedById: rest.CreatedById,
		CreatedTime: rest.CreatedTime,
		UpdatedById: rest.UpdatedById,
		UpdatedTime: rest.UpdatedTime,
		OOB:         rest.OOB,
		Removed:     rest.Removed,
	}
}

// ConvertToBaseEntityRefModel converts BaseEntityRefModelRest to BaseEntityRefModel
func ConvertToBaseEntityRefModel(rest rest.BaseEntityRefModelRest) model.BaseEntityRefModel {
	baseModel := ConvertToBaseEntityModel(rest.BaseEntityRest)
	return model.BaseEntityRefModel{
		BaseEntityModel: baseModel,
		RefId:           rest.RefId,
		RefModel:        rest.RefModel,
	}
}

// ConvertToBaseEntityRest converts BaseEntityModel to BaseEntityRest
func ConvertToBaseEntityRest(entityModel model.BaseEntityModel) rest.BaseEntityRest {
	return rest.BaseEntityRest{
		Id:          entityModel.Id,
		Name:        entityModel.Name,
		CreatedById: entityModel.CreatedById,
		CreatedTime: entityModel.CreatedTime,
		UpdatedById: entityModel.UpdatedById,
		UpdatedTime: entityModel.UpdatedTime,
		OOB:         entityModel.OOB,
		Removed:     entityModel.Removed,
	}
}

// ConvertToBaseEntityRefModelRest converts BaseEntityRefModel to BaseEntityRefModelRest
func ConvertToBaseEntityRefModelRest(entityModel model.BaseEntityRefModel) rest.BaseEntityRefModelRest {
	baseRest := ConvertToBaseEntityRest(entityModel.BaseEntityModel)
	return rest.BaseEntityRefModelRest{
		BaseEntityRest: baseRest,
		RefId:          entityModel.RefId,
		RefModel:       entityModel.RefModel,
	}
}
