package model

import (
	"github.com/uptrace/bun"
)

type RedhatAdvaisory struct {
	bun.BaseModel `bun:"deployment.redhat_advaisory"`
	BaseEntityModel
	RhId        string `json:"rhId"`
	Title       string `json:"title"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Severity    string `json:"severity"`
	ReleaseDate int64  `json:"releaseDate"`
	Summary     string `json:"summary"`
	Solution    string `json:"solution"`
	References  string `json:"references"`
	Rights      string `json:"rights"`
	Issued      string `json:"issued"`
	Updated     string `json:"updated"`
	CVEList     string `json:"cveList"`
	BugzillaIds string `json:"bugzillaIds"`
	Reboot      bool   `json:"reboot"`
	Restart     bool   `json:"restart"`
}
