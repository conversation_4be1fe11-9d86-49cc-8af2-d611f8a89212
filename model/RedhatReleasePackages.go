package model

import (
	"github.com/uptrace/bun"
)

type RedhatReleasePackages struct {
	bun.BaseModel `bun:"deployment.redhat_release_packages"`
	BaseEntityModel
	PkgName        string `json:"pkgName"`
	Filename       string `json:"filename"`
	Src            string `json:"src"`
	Release        string `json:"release"`
	Version        string `json:"version"`
	Sha256         string `json:"sha256"`
	Arch           string `json:"arch"`
	SrcVersionName string `json:"srcVersionName"`
	Sum            string `json:"sum"`
	SumType        string `json:"sumType"`
	Epoch          string `json:"epoch"`
}
