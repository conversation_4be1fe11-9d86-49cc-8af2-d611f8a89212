package model

import (
	"github.com/uptrace/bun"
)

type RedhatAgentNomination struct {
	bun.BaseModel `bun:"deployment.redhat_agent_nomination"`
	BaseEntityModel
	LastSyncTime     int64 `json:"lastSyncTime"`
	AssetId          int64 `json:"assetId"`
	LastRepoSyncDate int64 `json:"lastRepoSyncDate"`
	RedhatOsVariant  RedhatOsVariant
	Status           RedhatAgentNominationStatus
	Schedule         PatchSchedule
}

type RedhatOsVariant int

const (
	SERVER RedhatOsVariant = iota + 1
	WORKSTATION
)

func (oa RedhatOsVariant) String() string {
	switch oa {
	case SERVER:
		return "server"
	case WORKSTATION:
		return "workstation"
	default:
		return "server"
	}
}

func (oa RedhatOsVariant) ToVariant(osVariant string) RedhatOsVariant {
	switch osVariant {
	case "server":
		return SERVER
	case "workstation":
		return WORKSTATION
	default:
		return SERVER
	}
}

type RedhatAgentNominationStatus int

const (
	NOMINATION_PENDING RedhatAgentNominationStatus = iota + 1
	NOMINATION_INPROGRESS
	NOMINATION_SUCCESS
	NOMINATION_FAILED
)

func (s RedhatAgentNominationStatus) String() string {
	switch s {
	case NOMINATION_PENDING:
		return "pending"
	case NOMINATION_INPROGRESS:
		return "inprogress"
	case NOMINATION_SUCCESS:
		return "success"
	case NOMINATION_FAILED:
		return "failed"
	default:
		return "pending"
	}
}

func (s RedhatAgentNominationStatus) ToStatus(status string) RedhatAgentNominationStatus {
	switch status {
	case "pending":
		return NOMINATION_PENDING
	case "inprogress":
		return NOMINATION_INPROGRESS
	case "success":
		return NOMINATION_SUCCESS
	case "failed":
		return NOMINATION_FAILED
	default:
		return NOMINATION_PENDING
	}
}
