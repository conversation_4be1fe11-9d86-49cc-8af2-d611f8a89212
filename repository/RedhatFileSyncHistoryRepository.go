package repository

import (
	"context"
	"deployment/common"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type RedhatFileSyncHistoryRepository struct {
	dbConnection *bun.DB
}

func NewRedhatFileSyncHistoryRepository() *RedhatFileSyncHistoryRepository {
	return &RedhatFileSyncHistoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo RedhatFileSyncHistoryRepository) Create(history *model.RedhatFileSyncHistory) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(history).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return history.Id, nil
}

func (repo RedhatFileSyncHistoryRepository) Update(history *model.RedhatFileSyncHistory) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(history).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return history.Id, nil
}

func (repo RedhatFileSyncHistoryRepository) GetById(id int64, includeArchive bool) (model.RedhatFileSyncHistory, error) {
	var history model.RedhatFileSyncHistory
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&history).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&history).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return history, err
	}
	return history, nil
}

func (repo RedhatFileSyncHistoryRepository) FindByXmlName(xmlName string) ([]model.RedhatFileSyncHistory, error) {
	var histories []model.RedhatFileSyncHistory
	err := repo.dbConnection.NewSelect().Model(&histories).
		Where("xml_name = ?", xmlName).
		Where("removed = ?", false).
		Scan(context.Background())
	if err != nil {
		return histories, err
	}
	return histories, nil
}

func (repo RedhatFileSyncHistoryRepository) ExistsByXmlName(xmlName string) (bool, error) {
	count, err := repo.dbConnection.NewSelect().Model((*model.RedhatFileSyncHistory)(nil)).
		Where("xml_name = ?", xmlName).
		Where("removed = ?", false).
		Count(context.Background())
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (repo RedhatFileSyncHistoryRepository) GetAll(includeArchive bool) ([]model.RedhatFileSyncHistory, error) {
	var histories []model.RedhatFileSyncHistory
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&histories).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&histories).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return histories, err
	}
	return histories, nil
}

func (repo RedhatFileSyncHistoryRepository) GetAllByQuery(query string) ([]model.RedhatFileSyncHistory, error) {
	var histories []model.RedhatFileSyncHistory
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &histories)
	if err != nil {
		return histories, err
	}
	return histories, nil
}

func (repo RedhatFileSyncHistoryRepository) CountByQuery(query string) (int, error) {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo RedhatFileSyncHistoryRepository) Delete(id int64) error {
	_, err := repo.dbConnection.NewDelete().Model((*model.RedhatFileSyncHistory)(nil)).Where("id = ?", id).Exec(context.Background())
	return err
}

func (repo RedhatFileSyncHistoryRepository) SoftDelete(id int64) error {
	_, err := repo.dbConnection.NewUpdate().Model((*model.RedhatFileSyncHistory)(nil)).
		Set("removed = ?", true).
		Set("updated_time = ?", common.CurrentMillisecond()).
		Where("id = ?", id).
		Exec(context.Background())
	return err
}
