package repository

import (
	"context"
	"deployment/common"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type RedhatPatchRepository struct {
	dbConnection *bun.DB
}

func NewRedhatPatchRepository() *RedhatPatchRepository {
	return &RedhatPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo RedhatPatchRepository) Create(patch *model.RedhatPatch) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(patch).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patch.Id, nil
}

func (repo RedhatPatchRepository) Update(patch *model.RedhatPatch) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(patch).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return patch.Id, nil
}

func (repo RedhatPatchRepository) GetById(id int64, includeArchive bool) (model.RedhatPatch, error) {
	var patch model.RedhatPatch
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&patch).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&patch).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo RedhatPatchRepository) FindByPkgIdAndDistribution(pkgId, distribution string) (model.RedhatPatch, error) {
	var patch model.RedhatPatch
	err := repo.dbConnection.NewSelect().Model(&patch).
		Where("pkg_id = ?", pkgId).
		Where("distribution = ?", distribution).
		Where("removed = ?", false).
		Scan(context.Background())
	if err != nil {
		return patch, err
	}
	return patch, nil
}

func (repo RedhatPatchRepository) ExistsByPkgIdAndDistribution(pkgId, distribution string) (bool, error) {
	count, err := repo.dbConnection.NewSelect().Model((*model.RedhatPatch)(nil)).
		Where("pkg_id = ?", pkgId).
		Where("distribution = ?", distribution).
		Where("removed = ?", false).
		Count(context.Background())
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (repo RedhatPatchRepository) GetAll(includeArchive bool) ([]model.RedhatPatch, error) {
	var patches []model.RedhatPatch
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&patches).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&patches).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return patches, err
	}
	return patches, nil
}

func (repo RedhatPatchRepository) GetAllBySrcPkgName(srcPkgName string) ([]model.RedhatPatch, error) {
	var patches []model.RedhatPatch
	err := repo.dbConnection.NewSelect().Model(&patches).Where("src_pkg_name	 = ?", srcPkgName).Scan(context.Background())
	if err != nil {
		return patches, err
	}
	return patches, nil
}

func (repo RedhatPatchRepository) GetAllByQuery(query string, params []interface{}) ([]model.RedhatPatch, error) {
	var patches []model.RedhatPatch
	var err error
	if params != nil && len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &patches)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &patches)
	}
	if err != nil {
		return patches, err
	}
	return patches, nil
}

func (repo RedhatPatchRepository) CountByQuery(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if params != nil && len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo RedhatPatchRepository) Delete(id int64) error {
	_, err := repo.dbConnection.NewDelete().Model((*model.RedhatPatch)(nil)).Where("id = ?", id).Exec(context.Background())
	return err
}

func (repo RedhatPatchRepository) SoftDelete(id int64) error {
	_, err := repo.dbConnection.NewUpdate().Model((*model.RedhatPatch)(nil)).
		Set("removed = ?", true).
		Set("updated_time = ?", common.CurrentMillisecond()).
		Where("id = ?", id).
		Exec(context.Background())
	return err
}
