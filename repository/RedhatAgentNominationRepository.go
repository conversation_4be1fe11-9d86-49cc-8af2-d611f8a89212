package repository

import (
	"context"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type RedhatAgentNominationRepository struct {
	dbConnection *bun.DB
}

func NewRedhatAgentNominationRepository() *RedhatAgentNominationRepository {
	return &RedhatAgentNominationRepository{
		dbConnection: db.Connection,
	}
}

func (repo RedhatAgentNominationRepository) Create(nomination *model.RedhatAgentNomination) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(nomination).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return nomination.Id, nil
}

func (repo RedhatAgentNominationRepository) Update(nomination *model.RedhatAgentNomination) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(nomination).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return nomination.Id, nil
}

func (repo RedhatAgentNominationRepository) GetById(id int64, includeArchive bool) (model.RedhatAgentNomination, error) {
	var nomination model.RedhatAgentNomination
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&nomination).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&nomination).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return nomination, err
	}
	return nomination, nil
}

func (repo RedhatAgentNominationRepository) GetByRedhatOsVariant(variant model.RedhatOsVariant, includeArchive bool) (model.RedhatAgentNomination, error) {
	var nomination model.RedhatAgentNomination
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&nomination).Where("redhat_os_variant = ?", variant).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&nomination).Where("redhat_os_variant = ?", variant).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return nomination, err
	}
	return nomination, nil
}

func (repo RedhatAgentNominationRepository) GetAll(includeArchive bool) ([]model.RedhatAgentNomination, error) {
	var nominations []model.RedhatAgentNomination
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&nominations).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&nominations).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return nominations, err
	}
	return nominations, nil
}

func (repo RedhatAgentNominationRepository) GetAllByQuery(query string, params []interface{}) ([]model.RedhatAgentNomination, error) {
	var nominations []model.RedhatAgentNomination
	var err error
	if params != nil && len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &nominations)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &nominations)
	}
	if err != nil {
		return nominations, err
	}
	return nominations, nil
}

func (repo RedhatAgentNominationRepository) CountByQuery(query string, params []interface{}) (int, error) {
	var count int
	var err error
	if params != nil && len(params) > 0 {
		err = repo.dbConnection.NewRaw(query, params...).Scan(context.Background(), &count)
	} else {
		err = repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	}
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo RedhatAgentNominationRepository) ExistsByRedhatOsVariant(variant model.RedhatOsVariant) (bool, error) {
	count, err := repo.dbConnection.NewSelect().Model((*model.RedhatAgentNomination)(nil)).Where("redhat_os_variant = ?", variant).Where("removed = ?", false).Count(context.Background())
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
