package repository

import (
	"context"
	"deployment/common"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type RedhatAdvaisoryRepository struct {
	dbConnection *bun.DB
}

func NewRedhatAdvaisoryRepository() *RedhatAdvaisoryRepository {
	return &RedhatAdvaisoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo RedhatAdvaisoryRepository) Create(advisory *model.RedhatAdvaisory) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(advisory).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return advisory.Id, nil
}

func (repo RedhatAdvaisoryRepository) Update(advisory *model.RedhatAdvaisory) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(advisory).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return advisory.Id, nil
}

func (repo RedhatAdvaisoryRepository) GetById(id int64, includeArchive bool) (model.RedhatAdvaisory, error) {
	var advisory model.RedhatAdvaisory
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&advisory).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&advisory).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return advisory, err
	}
	return advisory, nil
}

func (repo RedhatAdvaisoryRepository) FindByRhId(rhId string) (model.RedhatAdvaisory, error) {
	var advisory model.RedhatAdvaisory
	err := repo.dbConnection.NewSelect().Model(&advisory).
		Where("rh_id = ?", rhId).
		Where("removed = ?", false).
		Scan(context.Background())
	if err != nil {
		return advisory, err
	}
	return advisory, nil
}

func (repo RedhatAdvaisoryRepository) ExistsByRhId(rhId string) (bool, error) {
	count, err := repo.dbConnection.NewSelect().Model((*model.RedhatAdvaisory)(nil)).
		Where("rh_id = ?", rhId).
		Where("removed = ?", false).
		Count(context.Background())
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (repo RedhatAdvaisoryRepository) GetAll(includeArchive bool) ([]model.RedhatAdvaisory, error) {
	var advisories []model.RedhatAdvaisory
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&advisories).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&advisories).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return advisories, err
	}
	return advisories, nil
}

func (repo RedhatAdvaisoryRepository) GetAllByQuery(query string) ([]model.RedhatAdvaisory, error) {
	var advisories []model.RedhatAdvaisory
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &advisories)
	if err != nil {
		return advisories, err
	}
	return advisories, nil
}

func (repo RedhatAdvaisoryRepository) CountByQuery(query string) (int, error) {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo RedhatAdvaisoryRepository) Delete(id int64) error {
	_, err := repo.dbConnection.NewDelete().Model((*model.RedhatAdvaisory)(nil)).Where("id = ?", id).Exec(context.Background())
	return err
}

func (repo RedhatAdvaisoryRepository) SoftDelete(id int64) error {
	_, err := repo.dbConnection.NewUpdate().Model((*model.RedhatAdvaisory)(nil)).
		Set("removed = ?", true).
		Set("updated_time = ?", common.CurrentMillisecond()).
		Where("id = ?", id).
		Exec(context.Background())
	return err
}
