package repository

import (
	"context"
	"deployment/common"
	"deployment/db"
	"deployment/model"
	"github.com/uptrace/bun"
)

type RedhatReleasePackagesRepository struct {
	dbConnection *bun.DB
}

func NewRedhatReleasePackagesRepository() *RedhatReleasePackagesRepository {
	return &RedhatReleasePackagesRepository{
		dbConnection: db.Connection,
	}
}

func (repo RedhatReleasePackagesRepository) Create(pkg *model.RedhatReleasePackages) (int64, error) {
	_, err := repo.dbConnection.NewInsert().Model(pkg).Returning("id").Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo RedhatReleasePackagesRepository) Update(pkg *model.RedhatReleasePackages) (int64, error) {
	_, err := repo.dbConnection.NewUpdate().Model(pkg).WherePK().Exec(context.Background())
	if err != nil {
		return 0, err
	}
	return pkg.Id, nil
}

func (repo RedhatReleasePackagesRepository) GetById(id int64, includeArchive bool) (model.RedhatReleasePackages, error) {
	var pkg model.RedhatReleasePackages
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&pkg).Where("id = ?", id).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&pkg).Where("id = ?", id).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

func (repo RedhatReleasePackagesRepository) FindBySha256(sha256 string) (model.RedhatReleasePackages, error) {
	var pkg model.RedhatReleasePackages
	err := repo.dbConnection.NewSelect().Model(&pkg).
		Where("sha256 = ?", sha256).
		Where("removed = ?", false).
		Scan(context.Background())
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

func (repo RedhatReleasePackagesRepository) ExistsBySha256(sha256 string) (bool, error) {
	count, err := repo.dbConnection.NewSelect().Model((*model.RedhatReleasePackages)(nil)).
		Where("sha256 = ?", sha256).
		Where("removed = ?", false).
		Count(context.Background())
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (repo RedhatReleasePackagesRepository) FindFirstBySrc(src string) (model.RedhatReleasePackages, error) {
	var pkg model.RedhatReleasePackages
	err := repo.dbConnection.NewSelect().Model(&pkg).
		Where("src = ?", src).
		Where("removed = ?", false).
		Limit(1).
		Scan(context.Background())
	if err != nil {
		return pkg, err
	}
	return pkg, nil
}

func (repo RedhatReleasePackagesRepository) GetAll(includeArchive bool) ([]model.RedhatReleasePackages, error) {
	var packages []model.RedhatReleasePackages
	var err error
	if includeArchive {
		err = repo.dbConnection.NewSelect().Model(&packages).Scan(context.Background())
	} else {
		err = repo.dbConnection.NewSelect().Model(&packages).Where("removed = ?", false).Scan(context.Background())
	}
	if err != nil {
		return packages, err
	}
	return packages, nil
}

func (repo RedhatReleasePackagesRepository) GetAllByQuery(query string) ([]model.RedhatReleasePackages, error) {
	var packages []model.RedhatReleasePackages
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &packages)
	if err != nil {
		return packages, err
	}
	return packages, nil
}

func (repo RedhatReleasePackagesRepository) GetAllByName(name string) ([]model.RedhatReleasePackages, error) {
	var packages []model.RedhatReleasePackages
	err := repo.dbConnection.NewSelect().Model(&packages).Where("name = ?", name).Scan(context.Background())
	if err != nil {
		return packages, err
	}
	return packages, nil
}

func (repo RedhatReleasePackagesRepository) CountByQuery(query string) (int, error) {
	var count int
	err := repo.dbConnection.NewRaw(query).Scan(context.Background(), &count)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (repo RedhatReleasePackagesRepository) Delete(id int64) error {
	_, err := repo.dbConnection.NewDelete().Model((*model.RedhatReleasePackages)(nil)).Where("id = ?", id).Exec(context.Background())
	return err
}

func (repo RedhatReleasePackagesRepository) SoftDelete(id int64) error {
	_, err := repo.dbConnection.NewUpdate().Model((*model.RedhatReleasePackages)(nil)).
		Set("removed = ?", true).
		Set("updated_time = ?", common.CurrentMillisecond()).
		Where("id = ?", id).
		Exec(context.Background())
	return err
}
