queries:
  - name: Server
    blocks:
      - reponame: "[rhel-10-server-x86_64-baseos-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 10 for x86_64 - BaseOS via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel10/10/x86_64/baseos/os"
        folderpath: "/redhat/10/x86_64/baseos/os"

      - reponame: "[rhel-10-server-x86_64-appstream-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 10 for x86_64 - Appstream via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel10/10/x86_64/appstream/os"
        folderpath: "/redhat/10/x86_64/appstream/os"
        
      - reponame: "[rhel-9-server-x86_64-baseos-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 9 for x86_64 - BaseOS via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel9/9/x86_64/baseos/os"
        folderpath: "/redhat/9/x86_64/baseos/os"

      - reponame: "[rhel-9-server-x86_64-appstream-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 9 for x86_64 - Appstream via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel9/9/x86_64/appstream/os"
        folderpath: "/redhat/9/x86_64/appstream/os"

      - reponame: "[rhel-8-server-x86_64-baseos-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 8 for x86_64 - BaseOS via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel8/8/x86_64/baseos/os"
        folderpath: "/redhat/8/x86_64/baseos/os"

      - reponame: "[rhel-8-server-x86_64-appstream-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 8 for x86_64 - Appstream via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel8/8/x86_64/appstream/os"
        folderpath: "/redhat/8/x86_64/appstream/os"

      - reponame: "[rhel-7-server-x86_64-rpms-ziro-cache]"
        displayname: "Red Hat Enterprise Linux 8 for x86_64 - SAP NetWeaver - Extended Update Support (Debug RPMs) via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel/server/7/7Server/x86_64/os"
        folderpath: "/redhat/7/7Server/x86_64/os"

      - reponame: "[rhel-7-server-x86_64-extras-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 7 Server x86_64 - Extras via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel/server/7/7Server/x86_64/extras/os"
        folderpath: "/redhat/7/7Server/x86_64/extras/os"

      - reponame: "[rhel-7-server-x86_64-optional-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 7 Server x86_64 - Optional via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel/server/7/7Server/x86_64/optional/os"
        folderpath: "/redhat/7/7Server/x86_64/optional/os"

  - name: Workstation
    blocks:
      - reponame: "[rhel-9-workstation-x86_64-appstream-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 9 for x86_64 - Appstream via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel9/9/x86_64/appstream/os"
        folderpath: "/redhat/9/9Workstation/x86_64/appstream/os"

      - reponame: "[rhel-9-workstation-x86_64-baseos-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 9 for x86_64 - BaseOS via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel9/9/x86_64/baseos/os"
        folderpath: "/redhat/9/9Workstation/x86_64/baseos/os"

      - reponame: "[rhel-8-workstation-x86_64-appstream-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 8 for x86_64 - Appstream via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel8/8/x86_64/appstream/os"
        folderpath: "/redhat/8/8Workstation/x86_64/appstream/os"

      - reponame: "[rhel-8-workstation-x86_64-baseos-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 8 for x86_64 - BaseOS via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel8/8/x86_64/baseos/os"
        folderpath: "/redhat/8/8Workstation/x86_64/baseos/os"

      - reponame: "[rhel-7-workstation-x86_64-rpms-ziro-cache]"
        displayname: "Red Hat Enterprise Linux 7 Workstation - Fastrack via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel/workstation/7/7Workstation/x86_64/os/"
        folderpath: "/redhat/7/7Workstation/x86_64/os"

      - reponame: "[rhel-7-workstation-x86_64-extras-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 7 Workstation x86_64 - via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel/workstation/7/7Workstation/x86_64/extras/os/"
        folderpath: "/redhat/7/7Workstation/x86_64/extras/os"

      - reponame: "[rhel-7-workstation-x86_64-optional-rpms-ziro-cache]"
        displayname: "Red Hat Enterpise Linux 7 Workstation x86_64 - via ZiroZen"
        baseurl: "https://cdn.redhat.com/content/dist/rhel/workstation/7/7Workstation/x86_64/optional/os/"
        folderpath: "/redhat/7/7Workstation/x86_64/optional/os" 
