package interfaces

import "deployment/rest"

// AuditServiceInterface defines the interface for audit operations
type AuditServiceInterface interface {
	Create(auditRest rest.AuditRest) (int64, error)
}

// AgentServiceInterface defines the interface for agent operations
type AgentServiceInterface interface {
	GetAsset(assetId string) (map[string]interface{}, error)
	GetAssetMetaDataById(assetId int64) (rest.AssetMetaDataRest, error)
}

// AgentTaskServiceInterface defines the interface for agent task operations
type AgentTaskServiceInterface interface {
	Create(agentTask rest.AgentTaskRest) (int64, error)
}

// Global variables to hold service implementations
var AuditServiceInstance AuditServiceInterface
var AgentServiceInstance AgentServiceInterface
var AgentTaskServiceInstance AgentTaskServiceInterface

// SetAuditService sets the audit service implementation
func SetAuditService(service AuditServiceInterface) {
	AuditServiceInstance = service
}

// GetAuditService returns the audit service implementation
func GetAuditService() AuditServiceInterface {
	return AuditServiceInstance
}

// SetAgentService sets the agent service implementation
func SetAgentService(service AgentServiceInterface) {
	AgentServiceInstance = service
}

// GetAgentService returns the agent service implementation
func GetAgentService() AgentServiceInterface {
	return AgentServiceInstance
}

// SetAgentTaskService sets the agent task service implementation
func SetAgentTaskService(service AgentTaskServiceInterface) {
	AgentTaskServiceInstance = service
}

// GetAgentTaskService returns the agent task service implementation
func GetAgentTaskService() AgentTaskServiceInterface {
	return AgentTaskServiceInstance
}
